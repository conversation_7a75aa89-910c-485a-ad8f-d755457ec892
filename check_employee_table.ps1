# Script to check and setup API_Employee table
. "C:\Users\<USER>\Documents\augment-projects\PO APPROVAL\database_service.ps1"

Write-Host "Checking API_Employee table..." -ForegroundColor Cyan

$connection = Get-DatabaseConnection

# Check if table exists
$checkTableQuery = @"
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='API_Employee' AND xtype='U')
BEGIN
    CREATE TABLE API_Employee (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        EmpCode NVARCHAR(50) NOT NULL UNIQUE,
        EmpName NVARCHAR(100),
        EmpId INT,
        Password NVARCHAR(100) NOT NULL,
        CreatedDate DATETIME DEFAULT GETDATE(),
        ModifiedDate DATETIME DEFAULT GETDATE(),
        CreatedBy NVARCHAR(50),
        ModifiedBy NVARCHAR(50),
        LastLoginDate DATETIME,
        IsActive BIT DEFAULT 1
    )
    PRINT 'API_Employee table created'
END
ELSE
BEGIN
    PRINT 'API_Employee table already exists'
END
"@

$command = New-Object System.Data.SqlClient.SqlCommand($checkTableQuery, $connection)
$command.ExecuteNonQuery()

# Check if test user exists
$checkUserQuery = @"
IF NOT EXISTS (SELECT 1 FROM API_Employee WHERE EmpCode = 'EMP-M')
BEGIN
    INSERT INTO API_Employee (EmpCode, EmpName, EmpId, Password, CreatedBy, IsActive)
    VALUES ('EMP-M', 'Test User', 1, '1234', 'SYSTEM', 1)
    PRINT 'Test user created'
END
ELSE
BEGIN
    PRINT 'Test user already exists'
END
"@

$command = New-Object System.Data.SqlClient.SqlCommand($checkUserQuery, $connection)
$command.ExecuteNonQuery()

# Check current users
$listUsersQuery = "SELECT EmpCode, EmpName, IsActive FROM API_Employee"
$command = New-Object System.Data.SqlClient.SqlCommand($listUsersQuery, $connection)
$reader = $command.ExecuteReader()

Write-Host "`nCurrent users in API_Employee table:" -ForegroundColor Green
while ($reader.Read()) {
    Write-Host "EmpCode: $($reader['EmpCode']), Name: $($reader['EmpName']), Active: $($reader['IsActive'])" -ForegroundColor Yellow
}

$reader.Close()
$connection.Close() 