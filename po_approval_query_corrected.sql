-- Combined PO Approval Query with UNION (Corrected version)
-- Server: WIN-PRK-SRV-01
-- Database: Icsoft
-- Description: Query to fetch both Material and Service PO approval details

USE Icsoft;

-- Material POs Query
SELECT
    COM.LOCATION,
    d.<PERSON>d,
    d.<PERSON>,
    d.<PERSON>,
    d.<PERSON>,
    SupName + ' | ' + SupCode AS gStrSupplierDisp,
    RawMatCode + ' | ' + RawMatName AS gStrMatDisp,
    GCode + ' | ' + Gradename AS gStrGradeDisp,
    d.<PERSON>d_<PERSON>,
    d.<PERSON>,
    ROUND(d.Rate, 4) AS Rate,
    d.CurrCode AS Currency,
    ((d.Rate * d.ExRate + Pack) - Discount) AS price,
    ROUND((d.Ord_Qty * ((d.Rate + d.Pack) - d.Discount)), 2) AS povalue,
    d.Raw<PERSON>at<PERSON>d,
    d.<PERSON>Po<PERSON>alue,
    d.EMPNAME AS POCreatedBY,
    d.<PERSON>,
    GH.HSNCode,
    Terms,
    QuotTerms,
    NULL AS MaxLimit,  -- Added for second query compatibility
    NULL AS MinLimit,  -- Added for second query compatibility
    NULL AS subcondesc,  -- Added for second query compatibility
    NULL AS ServiceHSNID,  -- Added for second query compatibility
    NULL AS TotalSubconPurchaseValue,  -- Added for second query compatibility
    NULL AS gstrCostCentreDisplay  -- Added for second query compatibility
FROM
    Invent_PoDetails_Query d
    LEFT OUTER JOIN IcSoftLedger.dbo.Acc_CostCentre Act
        ON d.ProfitCentreId = Act.CostCentreId
    LEFT OUTER JOIN GST_HSN_SAC_NO GH
        ON GH.Hsn_Sac_ID = d.HSNID
    INNER JOIN COMPANY COM ON COM.CompanyID = D.LocationID
    INNER JOIN (
        SELECT DISTINCT
            PS.Empid,
            P.Poid,
            PE.Minlimit,
            PE.Maxlimit
        FROM
            Invent_POApprovalLevels PE (NOLOCK)
            INNER JOIN Invent_POApprovalEmpLevel PS (NOLOCK)
                ON PE.levelid = PS.Levelid
            INNER JOIN Invent_PoDetails_Query P
                ON (
                    P.GTotal = 0
                    AND PE.Locationid LIKE '%'
                )
                OR (
                    (
                        CASE
                            WHEN PE.IsBasic = 'N'
                                THEN (P.GTotal + ISNULL((SELECT SUM(Amount) FROM Invent_PoprodWiseTax T WHERE T.Poid = P.Poid), 0))
                                ELSE P.GTotal
                        END * P.Exrate
                    ) <= (
                        SELECT MAX(Maxlimit)
                        FROM Invent_POApprovalLevels
                        WHERE Potype = P.AddnlParameter
                            AND Locationid LIKE '%'
                    )
                    AND (
                        CASE
                            WHEN PE.IsBasic = 'N'
                                THEN (P.GTotal + ISNULL((SELECT SUM(Amount) FROM Invent_PoprodWiseTax T WHERE T.Poid = P.Poid), 0))
                                ELSE P.GTotal
                        END * P.Exrate
                    ) >= PE.Minlimit
                )
        WHERE
            P.Postatus = 'Approval Pending'
            AND PS.Empid LIKE '%'
            AND PE.POType = P.AddnlParameter
            AND PE.Locationid LIKE '%'
            AND PE.Levelid NOT IN (
                SELECT DISTINCT Levelid
                FROM Invent_POApprovedEmployee A
                WHERE A.Poid = P.Poid
                    AND A.Levelid = PE.Levelid
                    AND PE.Locationid LIKE '%'
            )
            AND PS.Levelid IN (
                SELECT TOP 1 PS.Levelid
                FROM Invent_POApprovalLevels PE (NOLOCK)
                INNER JOIN Invent_POApprovalEmpLevel PS (NOLOCK)
                    ON PE.levelid = PS.Levelid
                WHERE
                    PE.Locationid LIKE '%'
                    AND PS.Empid NOT IN (
                        SELECT DISTINCT Empid
                        FROM Invent_POApprovedEmployee A
                        WHERE A.Poid = P.Poid
                            AND A.Levelid = PE.Levelid
                            AND PE.Locationid LIKE '%'
                    )
                    AND PoType = P.AddnlParameter
                    AND PS.Levelid NOT IN (
                        SELECT DISTINCT Levelid
                        FROM Invent_POApprovedEmployee A
                        WHERE A.Poid = P.Poid
                            AND A.Levelid = PE.Levelid
                            AND PE.Locationid LIKE '%'
                    )
                ORDER BY PE.MinLimit, PE.MaxLimit
            )
    ) A
    ON A.Poid = d.Poid
WHERE
    d.Addnlparameter LIKE '%'
    AND d.Locationid LIKE '%'
    AND d.PoStatus = 'Approval Pending'

UNION ALL

-- Service POs Query (Simplified - without complex approval logic for now)
SELECT
    com.Location,
    iscp.poid AS PoId,
    iscp.Pono AS PoNo,
    iscp.Podate AS PoDate,
    iscp.addnlparameter AS Addnlparameter,
    sc.supname + ' | ' + sc.supcode AS gStrSupplierDisp,
    ISNULL(r.rawmatcode + ' | ' + r.rawmatname, 'Service Item') AS gStrMatDisp,
    NULL AS gStrGradeDisp,  -- Not available in service query
    ISNULL(ispp.Ord_Qty, 1) AS Ord_Qty,
    ISNULL(ispp.UOM, 'Service') AS Uom,
    ISNULL(ispp.rate, 0) AS Rate,
    NULL AS Currency,  -- Not available in service query
    NULL AS price,  -- Not available in service query
    ISNULL(ispp.rate * ispp.Ord_Qty, 0) AS povalue,
    ISNULL(ispp.RawmatId, 0) AS RawmatId,
    NULL AS TotalPoValue,  -- Not available in service query
    e.empname AS POCreatedBY,
    NULL AS HSNID,  -- Simplified for now
    NULL AS HSNCode,  -- Simplified for now
    NULL AS Terms,  -- Not available in service query
    NULL AS QuotTerms,  -- Not available in service query
    NULL AS MaxLimit,
    NULL AS MinLimit,
    ISNULL(ispp.subcondesc, 'Service') AS subcondesc,
    NULL AS ServiceHSNID,
    NULL AS TotalSubconPurchaseValue,  -- Simplified for now
    NULL AS gstrCostCentreDisplay
FROM 
    invent_subconpurchase iscp
    INNER JOIN invent_supplier sc ON sc.supid = iscp.subconid
    INNER JOIN company com ON com.CompanyID = iscp.locationid
    INNER JOIN Employee e ON e.EmpId = iscp.entryempid 
    LEFT OUTER JOIN invent_subconpurchaseproduct ispp ON iscp.poid = ispp.poid 
    LEFT OUTER JOIN rawmaterial r ON ispp.rawmatId = r.rawmatId 
WHERE 
    iscp.PoStatus = 'Approval Pending'
    AND iscp.addnlparameter LIKE '%'
    AND iscp.locationid LIKE '%'

ORDER BY
    PoDate DESC,
    PoNo,
    PoId;

-- Query to get distinct Purchase Types (Addnlparameter values)
SELECT DISTINCT Addnlparameter AS PurchaseType
FROM (
    -- First query's source
    SELECT Addnlparameter FROM Invent_PoDetails_Query 
    WHERE PoStatus = 'Approval Pending'
    
    UNION
    
    -- Second query's source
    SELECT Addnlparameter FROM invent_subconpurchase
    WHERE PoStatus = 'Approval Pending'
) AS CombinedPurchaseTypes
ORDER BY Addnlparameter;
