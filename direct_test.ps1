# Direct test - bypass everything and test raw queries
$ErrorActionPreference = "Stop"

Write-Host "=== DIRECT DATABASE TEST ===" -ForegroundColor Yellow

# Test database connection
try {
    $connectionString = "Server=WIN-PRK-SRV-01;Database=Icsoft;Integrated Security=true;TrustServerCertificate=true;"
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    Write-Host "✅ Database connected" -ForegroundColor Green
} catch {
    Write-Host "❌ Database connection failed: $($_.Exception.Message)" -ForegroundColor Red
    exit
}

# Test 1: Raw count
Write-Host "`n--- Test 1: Raw Material PO Count ---" -ForegroundColor Cyan
try {
    $query1 = "SELECT COUNT(*) FROM Invent_PoDetails_Query WHERE PoStatus = 'Approval Pending'"
    $command1 = New-Object System.Data.SqlClient.SqlCommand($query1, $connection)
    $count1 = $command1.ExecuteScalar()
    Write-Host "Raw Material PO count: $count1" -ForegroundColor White
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Raw Service PO count
Write-Host "`n--- Test 2: Raw Service PO Count ---" -ForegroundColor Cyan
try {
    $query2 = "SELECT COUNT(*) FROM invent_subconpurchase WHERE PoStatus = 'Approval Pending'"
    $command2 = New-Object System.Data.SqlClient.SqlCommand($query2, $connection)
    $count2 = $command2.ExecuteScalar()
    Write-Host "Raw Service PO count: $count2" -ForegroundColor White
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Test our simple Material query
Write-Host "`n--- Test 3: Our Simple Material Query ---" -ForegroundColor Cyan
try {
    $query3 = @"
    SELECT TOP 5
        d.PoId,
        d.PoNo,
        d.PoStatus,
        COM.LOCATION
    FROM Invent_PoDetails_Query d
    INNER JOIN COMPANY COM ON COM.CompanyID = D.LocationID
    WHERE d.PoStatus = 'Approval Pending'
"@
    $command3 = New-Object System.Data.SqlClient.SqlCommand($query3, $connection)
    $adapter3 = New-Object System.Data.SqlClient.SqlDataAdapter($command3)
    $dataTable3 = New-Object System.Data.DataTable
    $adapter3.Fill($dataTable3)
    
    Write-Host "Simple Material query result: $($dataTable3.Rows.Count) rows" -ForegroundColor White
    if ($dataTable3.Rows.Count -gt 0) {
        foreach ($row in $dataTable3.Rows) {
            Write-Host "  PoId: $($row['PoId']), PoNo: $($row['PoNo']), Status: $($row['PoStatus'])" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Test our simple Service query
Write-Host "`n--- Test 4: Our Simple Service Query ---" -ForegroundColor Cyan
try {
    $query4 = @"
    SELECT TOP 5
        iscp.poid,
        iscp.Pono,
        iscp.PoStatus,
        com.Location
    FROM invent_subconpurchase iscp
    INNER JOIN company com ON com.CompanyID = iscp.locationid
    WHERE iscp.PoStatus = 'Approval Pending'
"@
    $command4 = New-Object System.Data.SqlClient.SqlCommand($query4, $connection)
    $adapter4 = New-Object System.Data.SqlClient.SqlDataAdapter($command4)
    $dataTable4 = New-Object System.Data.DataTable
    $adapter4.Fill($dataTable4)
    
    Write-Host "Simple Service query result: $($dataTable4.Rows.Count) rows" -ForegroundColor White
    if ($dataTable4.Rows.Count -gt 0) {
        foreach ($row in $dataTable4.Rows) {
            Write-Host "  PoId: $($row['poid']), PoNo: $($row['Pono']), Status: $($row['PoStatus'])" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Test the actual file queries
Write-Host "`n--- Test 5: File Query Test ---" -ForegroundColor Cyan
try {
    if (Test-Path "material_po_simple.sql") {
        $fileQuery = Get-Content "material_po_simple.sql" -Raw
        $command5 = New-Object System.Data.SqlClient.SqlCommand($fileQuery, $connection)
        $adapter5 = New-Object System.Data.SqlClient.SqlDataAdapter($command5)
        $dataTable5 = New-Object System.Data.DataTable
        $adapter5.Fill($dataTable5)
        Write-Host "File Material query result: $($dataTable5.Rows.Count) rows" -ForegroundColor White
    } else {
        Write-Host "material_po_simple.sql file not found!" -ForegroundColor Red
    }
} catch {
    Write-Host "File query error: $($_.Exception.Message)" -ForegroundColor Red
}

$connection.Close()
Write-Host "`n=== TEST COMPLETE ===" -ForegroundColor Yellow
