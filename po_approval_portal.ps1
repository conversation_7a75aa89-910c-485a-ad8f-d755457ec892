# PO Approval Portal - Modern UI Version
param([int]$Port = 8091)

# At the start of the file, add admin check
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
if (-not $isAdmin) {
    Write-Host "Please run this script as Administrator" -ForegroundColor Red
    Start-Process powershell -Verb RunAs -ArgumentList "-File `"$PSCommandPath`" -Port $Port"
    exit
}

# Import database service
. "C:\Users\<USER>\Documents\augment-projects\PO APPROVAL\database_service.ps1"

Write-Host "Starting PO Approval Portal (Modern UI)" -ForegroundColor Magenta
Write-Host "Port: $Port" -ForegroundColor Cyan

# Test database
$dbTest = Test-DatabaseConnection
if (-not $dbTest) {
    Write-Host "Database connection failed!" -ForegroundColor Red
    exit 1
}

# Configure firewall
netsh advfirewall firewall delete rule name="PO Approval Portal" 2>$null
netsh advfirewall firewall add rule name="PO Approval Portal" dir=in action=allow protocol=TCP localport=$Port

# Session management
$activeSessions = @{}
function New-SessionToken {
    return [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes([System.Guid]::NewGuid().ToString()))
}
function Test-Session {
    param($Request)
    $authHeader = $Request.Headers["Authorization"]
    if (-not $authHeader) { return $false }
    $token = $authHeader -replace "Bearer ", ""
    return $activeSessions.ContainsKey($token)
}

# User authentication
function Test-UserCredentials {
    param($Username, $Password)
    try {
        $connection = Get-DatabaseConnection
        $query = @"
            SELECT EmpCode, EmpName, EmpId 
            FROM API_Employee 
            WHERE EmpCode = @Username 
            AND Password = @Password 
            AND IsActive = 1
"@
        $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
        $command.Parameters.AddWithValue("@Username", $Username) | Out-Null
        $command.Parameters.AddWithValue("@Password", $Password) | Out-Null
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
        $dataTable = New-Object System.Data.DataTable
        $adapter.Fill($dataTable)
        $connection.Close()
        if ($dataTable.Rows.Count -eq 1) {
            return @{
                Success = $true
                EmpCode = $dataTable.Rows[0]["EmpCode"]
                EmpName = $dataTable.Rows[0]["EmpName"]
                EmpId = $dataTable.Rows[0]["EmpId"]
            }
        }
        return @{ Success = $false }
    } catch { return @{ Success = $false } }
}

# Query for pending POs
function Get-PendingPOs {
    $connection = Get-DatabaseConnection
    $query = @"
SELECT 
    c.Location,
    iss.SupName AS SupplierName,
    iss.Email,
    iss.Address,
    ip.PONo,
    ip.PODate,
    rm.RawMatCode,
    rm.RawMatName,
    ipp.Stock_Qty AS Qty,
    rm.uom,
    ipp.Rate,
    ipp.Discount,
    hsn.HSNCode,
    CASE 
        WHEN ip.TotalPOValue = 0 THEN 'OPEN PO'
        ELSE CAST(ip.TotalPOValue AS VARCHAR)
    END AS POValue,
    ip.Terms,
    ip.comments,
    ipp.Description,
    ip.AddnlParameter,
    'Invent_purchase' AS [Table],
    ip.POID
FROM INVENT_PURCHASE ip
INNER JOIN Invent_Purchaseproduct ipp ON ipp.POID = ip.POID
INNER JOIN RawMaterial rm ON rm.RawMatID = ipp.Rawmatid
INNER JOIN GST_HSN_SAC_NO hsn ON hsn.Hsn_Sac_ID = ipp.HSNID
INNER JOIN EMPLOYEE e ON e.EmpId = ip.OriginatedBy
INNER JOIN company c ON c.CompanyID = ip.LocationID
INNER JOIN Invent_Supplier iss ON iss.supid = ip.supid
WHERE postatus = 'Approval Pending'

UNION ALL

SELECT 
    c.Location,
    iss.Name AS SupplierName,
    iss.Email,
    iss.Address,
    ip.PONo,
    ip.PODate,
    rm.RawMatCode,
    rm.RawMatName,
    ipp.Stock_Qty AS Qty,
    rm.uom,
    ipp.Rate,
    ipp.Discount,
    hsn.HSNCode,
    CASE 
        WHEN ip.GTotal = 0 THEN 'OPEN PO'
        ELSE CAST(ip.GTotal AS VARCHAR)
    END AS POValue,
    ip.Terms,
    ip.comments,
    ipp.SubConDesc AS Description,
    ip.AddnlParameter,
    'Invent_SubConPurchase' AS [Table],
    ip.POID
FROM Invent_SubConPurchase ip
INNER JOIN Invent_SubconPurchaseProduct ipp ON ipp.POID = ip.POID
INNER JOIN RawMaterial rm ON rm.RawMatID = ipp.Rawmatid
INNER JOIN GST_HSN_SAC_NO hsn ON hsn.Hsn_Sac_ID = ipp.ServiceHSNId
INNER JOIN EMPLOYEE e ON e.EmpId = ip.EntryEmpId
INNER JOIN company c ON c.CompanyID = ip.LocationID
INNER JOIN PartyDetail iss ON iss.PartyID = ip.Subconid
WHERE postatus = 'Approval Pending'

UNION ALL

SELECT 
    c.Location,
    iss.Name AS SupplierName,
    iss.Email,
    iss.Address,
    ip.PONo,
    ip.PODate,
    rm.RawMatCode,
    rm.RawMatName,
    ipp.Qty,
    rm.uom,
    ipp.Rate,
    ipp.Discount,
    hsn.HSNCode,
    CASE 
        WHEN ip.GTotal = 0 THEN 'OPEN PO'
        ELSE CAST(ip.GTotal AS VARCHAR)
    END AS POValue,
    ip.Terms,
    ip.comments,
    ipp.Description,
    ip.AddnlParameter,
    'SubConPO' AS [Table],
    ip.POID
FROM SubConPO ip
INNER JOIN SubConPODetails ipp ON ipp.POID = ip.POID
INNER JOIN RawMaterial rm ON rm.RawMatID = ipp.ProdID
INNER JOIN GST_HSN_SAC_NO hsn ON hsn.Hsn_Sac_ID = ipp.ServiceHSNId
INNER JOIN EMPLOYEE e ON e.EmpId = ip.EntryEmpId
INNER JOIN company c ON c.CompanyID = ip.LocationID
INNER JOIN PartyDetail iss ON iss.PartyID = ip.Subconid
WHERE status = 'Approval Pending'
"@
    $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
    $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
    $dataTable = New-Object System.Data.DataTable
    $adapter.Fill($dataTable)
    $connection.Close()
    $results = @()
    foreach ($row in $dataTable.Rows) {
        $results += $row
    }
    return $results
}

# Approve/Reject PO
function Update-POStatus {
    param($Table, $POID, $Comments, $ApprovedBy, $Status)
    $connection = Get-DatabaseConnection
    switch ($Table) {
        'Invent_purchase' {
            $query = "UPDATE INVENT_PURCHASE SET postatus = @Status, comments = @Comments, ApprovedBy = @ApprovedBy WHERE POID = @POID"
        }
        'Invent_SubConPurchase' {
            $query = "UPDATE Invent_SubConPurchase SET postatus = @Status, comments = @Comments, ApprovedBy = @ApprovedBy WHERE POID = @POID"
        }
        'SubConPO' {
            $query = "UPDATE SubConPO SET status = @Status, comments = @Comments, ApprovedBy = @ApprovedBy WHERE POID = @POID"
        }
        default { return $false }
    }
    $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
    $command.Parameters.AddWithValue("@Status", $Status) | Out-Null
    $command.Parameters.AddWithValue("@Comments", $Comments) | Out-Null
    $command.Parameters.AddWithValue("@ApprovedBy", $ApprovedBy) | Out-Null
    $command.Parameters.AddWithValue("@POID", $POID) | Out-Null
    $result = $command.ExecuteNonQuery()
    $connection.Close()
    return $result -gt 0
}

# JSON response function
function Send-JSON {
    param($Response, $Data)
    $Response.ContentType = "application/json"
    $Response.Headers.Add("Access-Control-Allow-Origin", "*")
    $json = $Data | ConvertTo-Json -Depth 10 -Compress
    $buffer = [System.Text.Encoding]::UTF8.GetBytes($json)
    $Response.ContentLength64 = $buffer.Length
    $Response.OutputStream.Write($buffer, 0, $buffer.Length)
    $Response.OutputStream.Close()
}

# Modern HTML/JS UI
$htmlTemplate = @'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PO Approval Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .card { @apply bg-white rounded-lg shadow-lg p-6 mb-4; }
        .btn-primary { @apply px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors; }
        .btn-danger { @apply px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors; }
        .input-field { @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div id="app">
        <div class="min-h-screen flex items-center justify-center">
            <div class="max-w-md w-full space-y-8 card">
                <h2 class="text-center text-3xl font-extrabold text-gray-900">Loading...</h2>
                <p class="text-center text-gray-600">Please wait while the application initializes...</p>
            </div>
        </div>
    </div>
    <script>
        console.log('Initializing application...');
        
        window.onerror = function(msg, url, line, col, error) {
            console.error('Error:', msg, 'at line:', line, 'column:', col);
            console.error('Stack:', error && error.stack);
            document.getElementById('app').innerHTML = `
                <div class="min-h-screen flex items-center justify-center">
                    <div class="max-w-md w-full space-y-8 card">
                        <h2 class="text-center text-3xl font-extrabold text-red-600">Error</h2>
                        <p class="text-center text-gray-600">An error occurred while loading the application.</p>
                        <p class="text-sm text-red-500">${msg} (line: ${line})</p>
                        <button onclick="location.reload()" class="btn-primary w-full">Reload Page</button>
                    </div>
                </div>
            `;
            return false;
        };
    </script>
    <script>
        // Main application code will be injected here
        INJECT_APP_CODE
    </script>
</body>
</html>
'@

$jsCode = @'
console.log('Loading main application code...');

const API_BASE_URL = window.location.origin;
let authToken = localStorage.getItem('authToken');
let userData = localStorage.getItem('userData') ? JSON.parse(localStorage.getItem('userData')) : null;

async function fetchPOs() {
    console.log('Fetching POs...');
    const res = await fetch(`${API_BASE_URL}/api/pos`, {
        headers: { 'Authorization': `Bearer ${authToken}` }
    });
    return await res.json();
}

async function showDashboard() {
    console.log('Showing dashboard...');
    document.getElementById('app').innerHTML = `
        <nav class="bg-white shadow-lg p-4 flex justify-between items-center">
            <span class="text-xl font-semibold text-gray-800"><i class="fas fa-check-circle text-green-500 mr-2"></i>PO Approval Portal</span>
            <span class="text-gray-600"><i class="fas fa-user-circle mr-2"></i>${userData.name}</span>
            <button onclick="logout()" class="btn-danger">Logout</button>
        </nav>
        <main class="max-w-7xl mx-auto px-4 py-8">
            <div id="poList" class="space-y-4">
                <div class="text-center text-gray-600">Loading POs...</div>
            </div>
        </main>
    `;
    await loadPOs();
}

async function loadPOs() {
    console.log('Loading POs...');
    const poList = document.getElementById('poList');
    try {
        const pos = await fetchPOs();
        console.log('POs loaded:', pos.length);
        if (!pos.length) {
            poList.innerHTML = '<div class="text-center text-gray-500 p-8">No pending POs.</div>';
            return;
        }
        poList.innerHTML = pos.map(po => `
            <div class="card">
                <div class="flex justify-between items-center">
                    <div>
                        <div class="font-bold text-lg">PO No: ${po.PONo}</div>
                        <div class="text-gray-600">${po.SupplierName} | ${po.Location}</div>
                        <div class="text-gray-500 text-sm">${po.PODate ? new Date(po.PODate).toLocaleDateString() : ''}</div>
                    </div>
                    <div class="text-right">
                        <div class="font-semibold">Value: ₹${po.POValue}</div>
                        <div class="text-xs text-gray-400">${po.Table}</div>
                    </div>
                </div>
                <div class="mt-2">
                    <div><b>Material:</b> ${po.RawMatCode} - ${po.RawMatName}</div>
                    <div><b>Qty:</b> ${po.Qty} ${po.uom} | <b>Rate:</b> ${po.Rate}</div>
                    <div><b>HSN:</b> ${po.HSNCode}</div>
                    <div><b>Description:</b> ${po.Description || ''}</div>
                    <div><b>Terms:</b> ${po.Terms || ''}</div>
                    <div><b>Comments:</b> ${po.comments || ''}</div>
                </div>
                <div class="mt-4 flex gap-2">
                    <input type="text" id="comment_${po.Table}_${po.POID}" class="input-field flex-1" placeholder="Add comment...">
                    <button class="btn-primary" onclick="approvePO('${po.Table}', '${po.POID}')"><i class="fas fa-check"></i> Approve</button>
                    <button class="btn-danger" onclick="rejectPO('${po.Table}', '${po.POID}')"><i class="fas fa-times"></i> Reject</button>
                </div>
            </div>
        `).join('');
    } catch (error) {
        console.error('Error loading POs:', error);
        poList.innerHTML = '<div class="text-center text-red-500 p-8">Error loading POs. Please try again.</div>';
    }
}

async function approvePO(table, poid) {
    console.log('Approving PO:', table, poid);
    const comment = document.getElementById(`comment_${table}_${poid}`).value;
    try {
        const res = await fetch(`${API_BASE_URL}/api/approve`, {
            method: 'POST',
            headers: { 
                'Content-Type': 'application/json', 
                'Authorization': `Bearer ${authToken}` 
            },
            body: JSON.stringify({ 
                table, 
                poid, 
                comment, 
                status: 'Approved', 
                approvedBy: userData.username 
            })
        });
        const data = await res.json();
        if (data.success) {
            await loadPOs();
        } else {
            alert('Failed to approve PO');
        }
    } catch (error) {
        console.error('Error approving PO:', error);
        alert('Error approving PO');
    }
}

async function rejectPO(table, poid) {
    console.log('Rejecting PO:', table, poid);
    const comment = document.getElementById(`comment_${table}_${poid}`).value;
    try {
        const res = await fetch(`${API_BASE_URL}/api/approve`, {
            method: 'POST',
            headers: { 
                'Content-Type': 'application/json', 
                'Authorization': `Bearer ${authToken}` 
            },
            body: JSON.stringify({ 
                table, 
                poid, 
                comment, 
                status: 'Rejected', 
                approvedBy: userData.username 
            })
        });
        const data = await res.json();
        if (data.success) {
            await loadPOs();
        } else {
            alert('Failed to reject PO');
        }
    } catch (error) {
        console.error('Error rejecting PO:', error);
        alert('Error rejecting PO');
    }
}

async function showLogin() {
    console.log('Showing login form...');
    document.getElementById('app').innerHTML = `
        <div class="min-h-screen flex items-center justify-center">
            <div class="max-w-md w-full space-y-8 card">
                <h2 class="text-center text-3xl font-extrabold text-gray-900">PO Approval Portal</h2>
                <form id="loginForm" class="space-y-6">
                    <div>
                        <input id="username" name="username" type="text" required class="input-field" placeholder="Username">
                    </div>
                    <div>
                        <input id="password" name="password" type="password" required class="input-field" placeholder="Password">
                    </div>
                    <div id="loginError" class="text-red-600 text-center hidden"></div>
                    <button type="submit" class="btn-primary w-full">Sign in</button>
                </form>
            </div>
        </div>
    `;
    
    document.getElementById('loginForm').onsubmit = async (e) => {
        e.preventDefault();
        console.log('Login form submitted...');
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        try {
            const res = await fetch(`${API_BASE_URL}/api/login`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ username, password })
            });
            const data = await res.json();
            console.log('Login response:', data.success ? 'Success' : 'Failed');
            if (data.success) {
                localStorage.setItem('authToken', data.token);
                localStorage.setItem('userData', JSON.stringify(data.user));
                authToken = data.token;
                userData = data.user;
                await showDashboard();
            } else {
                document.getElementById('loginError').textContent = data.message || 'Invalid credentials';
                document.getElementById('loginError').classList.remove('hidden');
            }
        } catch (error) {
            console.error('Login error:', error);
            document.getElementById('loginError').textContent = 'Network error occurred';
            document.getElementById('loginError').classList.remove('hidden');
        }
    };
}

function logout() {
    console.log('Logging out...');
    localStorage.removeItem('authToken');
    localStorage.removeItem('userData');
    authToken = null;
    userData = null;
    showLogin();
}

// Initialize the application
if (!authToken || !userData) {
    showLogin();
} else {
    showDashboard();
}
'@

# Update the HTML template with the JavaScript code
$htmlWithJs = $htmlTemplate.Replace('INJECT_APP_CODE', $jsCode)

# Simple HTTP Server
try {
    # Clean up any existing registrations
    $null = netsh http delete urlacl url=http://+:$Port/ 2>&1
    $null = netsh http add urlacl url=http://+:$Port/ user=Everyone
    
    # Create basic listener
    $listener = New-Object System.Net.HttpListener
    $listener.Prefixes.Add("http://+:$Port/")
    
    Write-Host "Starting server..." -ForegroundColor Yellow
    $listener.Start()
    Write-Host "Server is running at http://localhost:$Port/" -ForegroundColor Green
    
    while ($true) {
        $context = $listener.GetContext()
        $request = $context.Request
        $response = $context.Response
        
        Write-Host "[$($request.HttpMethod)] $($request.Url.LocalPath)" -ForegroundColor Gray
        
        # Set CORS headers
        $response.Headers.Add("Access-Control-Allow-Origin", "*")
        $response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
        $response.Headers.Add("Access-Control-Allow-Headers", "Content-Type, Authorization")
        
        if ($request.HttpMethod -eq 'OPTIONS') {
            $response.StatusCode = 200
            $response.Close()
            continue
        }
        
        try {
            switch -regex ($request.Url.LocalPath) {
                '^/$' {
                    $response.ContentType = "text/html"
                    $buffer = [System.Text.Encoding]::UTF8.GetBytes($htmlWithJs)
                    $response.ContentLength64 = $buffer.Length
                    $response.OutputStream.Write($buffer, 0, $buffer.Length)
                }
                '^/api/login$' {
                    if ($request.HttpMethod -ne 'POST') { 
                        $response.StatusCode = 405 
                    } else {
                        $reader = New-Object System.IO.StreamReader($request.InputStream)
                        $body = $reader.ReadToEnd()
                        $credentials = $body | ConvertFrom-Json
                        $authResult = Test-UserCredentials -Username $credentials.username -Password $credentials.password
                        if ($authResult.Success) {
                            $token = New-SessionToken
                            $activeSessions[$token] = @{
                                Username = $credentials.username
                                EmpName = $authResult.EmpName
                                EmpId = $authResult.EmpId
                                LastAccess = Get-Date
                            }
                            Send-JSON -Response $response -Data @{
                                success = $true
                                token = $token
                                user = @{
                                    username = $credentials.username
                                    name = $authResult.EmpName
                                    empId = $authResult.EmpId
                                }
                            }
                        } else {
                            Send-JSON -Response $response -Data @{
                                success = $false
                                message = "Invalid credentials"
                            }
                        }
                    }
                }
                '^/api/pos$' {
                    if (-not (Test-Session -Request $request)) {
                        $response.StatusCode = 401
                    } else {
                        $pos = Get-PendingPOs
                        Send-JSON -Response $response -Data $pos
                    }
                }
                '^/api/approve$' {
                    if ($request.HttpMethod -ne 'POST') {
                        $response.StatusCode = 405
                    } elseif (-not (Test-Session -Request $request)) {
                        $response.StatusCode = 401
                    } else {
                        $reader = New-Object System.IO.StreamReader($request.InputStream)
                        $body = $reader.ReadToEnd()
                        $data = $body | ConvertFrom-Json
                        $ok = Update-POStatus -Table $data.table -POID $data.poid -Comments $data.comment -ApprovedBy $data.approvedBy -Status $data.status
                        Send-JSON -Response $response -Data @{ success = $ok }
                    }
                }
                default {
                    $response.StatusCode = 404
                }
            }
        }
        catch {
            Write-Host "Error: $_" -ForegroundColor Red
            $response.StatusCode = 500
        }
        finally {
            $response.Close()
        }
    }
}
catch {
    Write-Host "Server error: $_" -ForegroundColor Red
}
finally {
    if ($listener) {
        $listener.Stop()
        $listener.Close()
    }
} 