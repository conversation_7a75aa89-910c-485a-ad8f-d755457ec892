# Enhanced PO Approval Frontend - Modern UI Version
param([int]$Port = 8090)

# Import database service with full path
. "C:\Users\<USER>\Documents\augment-projects\PO APPROVAL\database_service.ps1"

Write-Host "Starting Enhanced PO Approval Frontend" -ForegroundColor Magenta
Write-Host "Port: $Port" -ForegroundColor Cyan

# Test database
$dbTest = Test-DatabaseConnection
if (-not $dbTest) {
    Write-Host "Database connection failed!" -ForegroundColor Red
    exit 1
}

# Configure firewall
netsh advfirewall firewall delete rule name="PO Frontend Enhanced" 2>$null
netsh advfirewall firewall add rule name="PO Frontend Enhanced" dir=in action=allow protocol=TCP localport=$Port

# Enhanced JSON response function with CORS and compression support
function Send-JSON {
    param($Response, $Data)
    $Response.ContentType = "application/json"
    $Response.Headers.Add("Access-Control-Allow-Origin", "*")
    $Response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
    $Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type")
    $Response.Headers.Add("X-Content-Type-Options", "nosniff")
    $Response.Headers.Add("X-Frame-Options", "DENY")
    $Response.Headers.Add("Content-Security-Policy", "default-src 'self'")
    
    $json = $Data | ConvertTo-Json -Depth 10 -Compress
    $buffer = [System.Text.Encoding]::UTF8.GetBytes($json)
    $Response.ContentLength64 = $buffer.Length
    $Response.OutputStream.Write($buffer, 0, $buffer.Length)
    $Response.OutputStream.Close()
}

# Function to serve static files with proper MIME types
function Send-StaticFile {
    param($Response, $FilePath, $ContentType)
    $Response.ContentType = $ContentType
    $Response.Headers.Add("Cache-Control", "public, max-age=3600")
    $buffer = [System.IO.File]::ReadAllBytes($FilePath)
    $Response.ContentLength64 = $buffer.Length
    $Response.OutputStream.Write($buffer, 0, $buffer.Length)
    $Response.OutputStream.Close()
}

# Create HTML template with modern UI
$htmlTemplate = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced PO Approval System</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .loading { @apply animate-pulse bg-gray-200; }
        .btn-primary { @apply px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors; }
        .btn-secondary { @apply px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors; }
        .card { @apply bg-white rounded-lg shadow-lg p-6 mb-4; }
        .input-field { @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500; }
        .form-label { @apply block text-sm font-medium text-gray-700; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Login Form -->
    <div id="loginForm" class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    PO Approval System
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    Please sign in to continue
                </p>
            </div>
            <form id="loginFormElement" class="mt-8 space-y-6">
                <div class="rounded-md shadow-sm -space-y-px">
                    <div>
                        <label for="username" class="sr-only">Username</label>
                        <input id="username" name="username" type="text" required 
                            class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                            placeholder="Username">
                    </div>
                    <div>
                        <label for="password" class="sr-only">Password</label>
                        <input id="password" name="password" type="password" required 
                            class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                            placeholder="Password">
                    </div>
                </div>

                <div>
                    <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fas fa-sign-in-alt"></i>
                        </span>
                        Sign in
                    </button>
                </div>
                <div id="loginError" class="text-red-600 text-center hidden"></div>
            </form>
        </div>
    </div>

    <!-- Main Application (hidden by default) -->
    <div id="mainApp" class="hidden">
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <span class="text-xl font-semibold text-gray-800">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        PO Approval System
                    </span>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="userInfo" class="text-gray-600">
                        <i class="fas fa-user-circle mr-2"></i>
                        <span id="userName"></span>
                    </span>
                    <button id="refreshBtn" class="btn-secondary">
                        <i class="fas fa-sync-alt mr-2"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <main class="max-w-7xl mx-auto px-4 py-8">
        <div class="filter-section">
            <div class="flex-1">
                <label class="block text-sm font-medium text-gray-700">Purchase Type</label>
                <select id="purchaseTypeFilter" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                    <option value="">All Types</option>
                </select>
            </div>
            <div class="flex-1">
                <label class="block text-sm font-medium text-gray-700">Date Range</label>
                <div class="flex space-x-2">
                    <input type="date" id="startDate" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                    <input type="date" id="endDate" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th class="table-header">PO No</th>
                            <th class="table-header">Date</th>
                            <th class="table-header">Type</th>
                            <th class="table-header">Supplier</th>
                            <th class="table-header">Material</th>
                            <th class="table-header">Quantity</th>
                            <th class="table-header">Value</th>
                            <th class="table-header">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="poTable" class="divide-y divide-gray-200">
                        <!-- Data will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <!-- Modal for PO Details -->
    <div id="poModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center pb-3">
                <h3 class="text-xl font-semibold text-gray-900" id="modalTitle">PO Details</h3>
                <button id="closeModal" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="modalContent" class="mt-4">
                <!-- Modal content will be populated here -->
            </div>
            <div class="mt-4 flex justify-end space-x-3">
                <button id="rejectBtn" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
                    <i class="fas fa-times mr-2"></i>Reject
                </button>
                <button id="approveBtn" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                    <i class="fas fa-check mr-2"></i>Approve
                </button>
            </div>
        </div>
    </div>
    </div>

    <script>
        // Main JavaScript code will be injected here
    </script>
</body>
</html>
"@

# Create JavaScript code with enhanced functionality
$jsCode = @"
const API_BASE_URL = 'http://localhost:$Port';
let currentPOData = [];

// Check login status on page load
document.addEventListener('DOMContentLoaded', () => {
    const urlParams = new URLSearchParams(window.location.search);
    const username = urlParams.get('username');
    const password = urlParams.get('password');

    if (username && password) {
        // Auto-login with URL parameters
        handleLogin(username, password);
    }
});

// Handle login form submission
document.getElementById('loginFormElement').addEventListener('submit', async (e) => {
    e.preventDefault();
    console.log('Form submitted');
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    await handleLogin(username, password);
});

async function handleLogin(username, password) {
    try {
        console.log('Attempting login...');
        const loginError = document.getElementById('loginError');
        loginError.classList.add('hidden');

        const response = await fetch(`\${API_BASE_URL}/api/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, password })
        });

        console.log('Login response received');
        const data = await response.json();
        console.log('Login response:', data);

        if (data.success) {
            console.log('Login successful');
            // Store auth data
            localStorage.setItem('authToken', data.token);
            localStorage.setItem('userData', JSON.stringify(data.user));
            
            // Update UI
            document.getElementById('userName').textContent = data.user.name;
            
            // Hide login form and show main app
            const loginForm = document.getElementById('loginForm');
            const mainApp = document.getElementById('mainApp');
            
            loginForm.style.display = 'none';
            mainApp.style.display = 'block';
            
            // Load initial data
            console.log('Loading initial data...');
            await fetchPurchaseTypes();
            await fetchPendingPOs();
        } else {
            console.log('Login failed:', data.message);
            loginError.textContent = data.message || 'Invalid credentials';
            loginError.classList.remove('hidden');
        }
    } catch (error) {
        console.error('Login error:', error);
        const loginError = document.getElementById('loginError');
        loginError.textContent = 'An error occurred during login. Please try again.';
        loginError.classList.remove('hidden');
    }
}

// Utility functions
const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
};

const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR'
    }).format(value);
};

// API calls with authentication
async function fetchPurchaseTypes() {
    try {
        console.log('Fetching purchase types...');
        const response = await fetch(`\${API_BASE_URL}/api/purchase-types`, {
            headers: {
                'Authorization': `Bearer \${localStorage.getItem('authToken')}`
            }
        });
        if (!response.ok) throw new Error('Failed to fetch purchase types');
        
        const types = await response.json();
        console.log('Purchase types:', types);
        
        const select = document.getElementById('purchaseTypeFilter');
        select.innerHTML = '<option value="">All Types</option>';
        types.forEach(type => {
            const option = document.createElement('option');
            option.value = type;
            option.textContent = type;
            select.appendChild(option);
        });
    } catch (error) {
        console.error('Error fetching purchase types:', error);
    }
}

async function fetchPendingPOs(purchaseType = '') {
    try {
        console.log('Fetching pending POs...');
        const response = await fetch(`\${API_BASE_URL}/api/pending-pos?type=\${encodeURIComponent(purchaseType)}`, {
            headers: {
                'Authorization': `Bearer \${localStorage.getItem('authToken')}`
            }
        });
        if (!response.ok) throw new Error('Failed to fetch pending POs');
        
        currentPOData = await response.json();
        console.log('Pending POs:', currentPOData);
        renderPOTable(currentPOData);
    } catch (error) {
        console.error('Error fetching pending POs:', error);
    }
}

// UI Rendering
function renderPOTable(data) {
    const tbody = document.getElementById('poTable');
    tbody.innerHTML = '';

    data.forEach(po => {
        const tr = document.createElement('tr');
        tr.className = 'hover:bg-gray-50';
        tr.innerHTML = `
            <td class="table-cell">\${po.PoNo}</td>
            <td class="table-cell">\${formatDate(po.PoDate)}</td>
            <td class="table-cell">
                <span class="status-badge bg-blue-100 text-blue-800">
                    \${po.POType}
                </span>
            </td>
            <td class="table-cell">\${po.Supplier}</td>
            <td class="table-cell">\${po.Material}</td>
            <td class="table-cell">\${po.Quantity} \${po.UOM}</td>
            <td class="table-cell">\${formatCurrency(po.POValue)}</td>
            <td class="table-cell">
                <button onclick="showPODetails('\${po.PoId}')" class="btn-primary">
                    <i class="fas fa-eye mr-1"></i> View
                </button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

function showPODetails(poId) {
    const po = currentPOData.find(p => p.PoId === poId);
    if (!po) return;

    const modal = document.getElementById('poModal');
    const modalContent = document.getElementById('modalContent');
    
    modalContent.innerHTML = `
        <div class="grid grid-cols-2 gap-4">
            <div class="space-y-4">
                <div class="card">
                    <h4 class="font-semibold mb-2">PO Information</h4>
                    <p><span class="text-gray-600">PO Number:</span> \${po.PoNo}</p>
                    <p><span class="text-gray-600">Date:</span> \${formatDate(po.PoDate)}</p>
                    <p><span class="text-gray-600">Type:</span> \${po.POType}</p>
                </div>
                <div class="card">
                    <h4 class="font-semibold mb-2">Supplier Details</h4>
                    <p>\${po.Supplier}</p>
                </div>
            </div>
            <div class="space-y-4">
                <div class="card">
                    <h4 class="font-semibold mb-2">Material Details</h4>
                    <p><span class="text-gray-600">Material:</span> \${po.Material}</p>
                    <p><span class="text-gray-600">Grade:</span> \${po.Grade || 'N/A'}</p>
                    <p><span class="text-gray-600">HSN Code:</span> \${po.HSNCode || 'N/A'}</p>
                </div>
                <div class="card">
                    <h4 class="font-semibold mb-2">Value Details</h4>
                    <p><span class="text-gray-600">Quantity:</span> \${po.Quantity} \${po.UOM}</p>
                    <p><span class="text-gray-600">Rate:</span> \${formatCurrency(po.Rate)}</p>
                    <p><span class="text-gray-600">Total Value:</span> \${formatCurrency(po.POValue)}</p>
                </div>
            </div>
        </div>
    `;

    modal.classList.remove('hidden');
}

// Event Listeners
document.getElementById('closeModal').addEventListener('click', () => {
    document.getElementById('poModal').classList.add('hidden');
});

document.getElementById('purchaseTypeFilter').addEventListener('change', (e) => {
    fetchPendingPOs(e.target.value);
});

document.getElementById('refreshBtn').addEventListener('click', () => {
    const purchaseType = document.getElementById('purchaseTypeFilter').value;
    fetchPendingPOs(purchaseType);
});

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    fetchPurchaseTypes();
    fetchPendingPOs();
});
"@

# Create the HTTP listener
$listener = New-Object System.Net.HttpListener
$listener.Prefixes.Add("http://localhost:$Port/")
$listener.Start()

Write-Host "Server is running on http://localhost:$Port/" -ForegroundColor Green

while ($listener.IsListening) {
    $context = $listener.GetContext()
    $request = $context.Request
    $response = $context.Response

    $path = $request.Url.LocalPath
    Write-Host "[$($request.HttpMethod)] $path" -ForegroundColor Cyan

    switch -regex ($path) {
        '^/$' {
            $response.ContentType = "text/html"
            $htmlWithJs = $htmlTemplate -replace '</script>', "$jsCode</script>"
            $buffer = [System.Text.Encoding]::UTF8.GetBytes($htmlWithJs)
            $response.ContentLength64 = $buffer.Length
            $response.OutputStream.Write($buffer, 0, $buffer.Length)
            $response.OutputStream.Close()
        }
        
        '^/api/login$' {
            if ($request.HttpMethod -ne 'POST') {
                $response.StatusCode = 405
                $response.Close()
                continue
            }

            $reader = New-Object System.IO.StreamReader($request.InputStream)
            $body = $reader.ReadToEnd()
            
            try {
                $credentials = $body | ConvertFrom-Json
                $authResult = Test-UserCredentials -Username $credentials.username -Password $credentials.password
                
                if ($authResult.Success) {
                    $token = New-SessionToken
                    $activeSessions[$token] = @{
                        Username = $credentials.username
                        EmpName = $authResult.EmpName
                        EmpId = $authResult.EmpId
                        LastAccess = Get-Date
                    }

                    Send-JSON -Response $response -Data @{
                        success = $true
                        token = $token
                        user = @{
                            username = $credentials.username
                            name = $authResult.EmpName
                            empId = $authResult.EmpId
                        }
                    }
                } else {
                    Send-JSON -Response $response -Data @{
                        success = $false
                        message = "Invalid credentials"
                    }
                }
            }
            catch {
                Send-JSON -Response $response -Data @{
                    success = $false
                    message = "Error processing login request"
                }
            }
        }
        
        '^/api/purchase-types$' {
            if (-not (Test-Session -Request $request)) {
                $response.StatusCode = 401
                $response.Close()
                continue
            }
            $types = Get-PurchaseTypes
            Send-JSON -Response $response -Data $types
        }
        
        '^/api/pending-pos$' {
            if (-not (Test-Session -Request $request)) {
                $response.StatusCode = 401
                $response.Close()
                continue
            }
            $purchaseType = $request.QueryString["type"]
            if ([string]::IsNullOrEmpty($purchaseType)) {
                $purchaseType = '%'
            }
            $pos = Get-PendingPOs -PurchaseType $purchaseType
            Send-JSON -Response $response -Data $pos
        }
        
        default {
            $response.StatusCode = 404
            $response.Close()
        }
    }
} 