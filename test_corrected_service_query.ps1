# Test the corrected service query
. "C:\Users\<USER>\Documents\augment-projects\PO APPROVAL\database_service.ps1"

Write-Host "=== TESTING CORRECTED SERVICE QUERY ===" -ForegroundColor Yellow

try {
    $connection = Get-DatabaseConnection
    
    Write-Host "Testing corrected Service PO query..." -ForegroundColor Cyan
    $serviceQuery = Get-Content "service_po_query.sql" -Raw
    
    $command = New-Object System.Data.SqlClient.SqlCommand($serviceQuery, $connection)
    $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
    $dataTable = New-Object System.Data.DataTable
    $adapter.Fill($dataTable)
    $connection.Close()
    
    Write-Host "Service POs found: $($dataTable.Rows.Count)" -ForegroundColor Green
    
    if ($dataTable.Rows.Count -gt 0) {
        Write-Host "Sample Service PO:" -ForegroundColor Yellow
        $row = $dataTable.Rows[0]
        Write-Host "  PoId: $($row['poid'])" -ForegroundColor White
        Write-Host "  PoNo: $($row['pono'])" -ForegroundColor White
        Write-Host "  Status: $($row['postatus'])" -ForegroundColor White
        Write-Host "  Supplier: $($row['gStrSupplierDisp'])" -ForegroundColor White
        Write-Host "  Material: $($row['gStrMatDisp'])" -ForegroundColor White
        Write-Host "  Type: $($row['addnlparameter'])" -ForegroundColor White
        Write-Host "  Value: $($row['povalue'])" -ForegroundColor White
    }
    
    Write-Host "✅ Service query test completed successfully!" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Service PO Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Line Number: $($_.InvocationInfo.ScriptLineNumber)" -ForegroundColor Red
}
