-- PO Approval Query for Remote Execution
-- Server: WIN-PRK-SRV-01
-- Database: Icsoft
-- Description: Query to fetch PO approval details with all related information

USE Icsoft;

-- Combined PO Approval Query with UNION (Fixed version)
SELECT
    COM.LOCATION,
    d.<PERSON>,
    d.<PERSON>,
    d.<PERSON>,
    d.<PERSON>,
    SupName + ' | ' + SupCode AS gStrSupplierDisp,
    RawMatCode + ' | ' + RawMatName AS gStrMatDisp,
    GCode + ' | ' + Gradename AS gStrGradeDisp,
    d.Ord_<PERSON>ty,
    d.Uom,
    ROUND(d.Rate, 4) AS Rate,
    d.CurrCode AS Currency,
    ((d.Rate * d.ExRate + Pack) - Discount) AS price,
    ROUND((d.Ord_Qty * ((d.Rate + d.Pack) - d.Discount)), 2) AS povalue,
    d.<PERSON>,
    d.<PERSON>o<PERSON>,
    d.<PERSON> AS POCreatedBY,
    d.<PERSON>,
    G<PERSON><PERSON>HSNCode,
    Terms,
    QuotTerms,
    NULL AS MaxLimit,  -- Added for second query compatibility
    NULL AS MinLimit,  -- Added for second query compatibility
    NULL AS subcondesc,  -- Added for second query compatibility
    NULL AS ServiceHSNID,  -- Added for second query compatibility
    NULL AS TotalSubconPurchaseValue,  -- Added for second query compatibility
    NULL AS gstrCostCentreDisplay  -- Added for second query compatibility
FROM
    Invent_PoDetails_Query d
    LEFT OUTER JOIN IcSoftLedger.dbo.Acc_CostCentre Act
        ON d.ProfitCentreId = Act.CostCentreId
    LEFT OUTER JOIN GST_HSN_SAC_NO GH
        ON GH.Hsn_Sac_ID = d.HSNID
    INNER JOIN COMPANY COM ON COM.CompanyID = D.LocationID
    INNER JOIN (
        SELECT DISTINCT
            PS.Empid,
            P.Poid,
            PE.Minlimit,
            PE.Maxlimit
        FROM
            Invent_POApprovalLevels PE (NOLOCK)
            INNER JOIN Invent_POApprovalEmpLevel PS (NOLOCK)
                ON PE.levelid = PS.Levelid
            INNER JOIN Invent_PoDetails_Query P
                ON (
                    P.GTotal = 0
                    AND PE.Locationid LIKE '%'
                )
                OR (
                    (
                        CASE
                            WHEN PE.IsBasic = 'N'
                                THEN (P.GTotal + ISNULL((SELECT SUM(Amount) FROM Invent_PoprodWiseTax T WHERE T.Poid = P.Poid), 0))
                                ELSE P.GTotal
                        END * P.Exrate
                    ) <= (
                        SELECT MAX(Maxlimit)
                        FROM Invent_POApprovalLevels
                        WHERE Potype = P.AddnlParameter
                            AND Locationid LIKE '%'
                    )
                    AND (
                        CASE
                            WHEN PE.IsBasic = 'N'
                                THEN (P.GTotal + ISNULL((SELECT SUM(Amount) FROM Invent_PoprodWiseTax T WHERE T.Poid = P.Poid), 0))
                                ELSE P.GTotal
                        END * P.Exrate
                    ) >= PE.Minlimit
                )
        WHERE
            P.Postatus = 'Approval Pending'
            AND PS.Empid LIKE '%'
            AND PE.POType = P.AddnlParameter
            AND PE.Locationid LIKE '%'
            AND PE.Levelid NOT IN (
                SELECT DISTINCT Levelid
                FROM Invent_POApprovedEmployee A
                WHERE A.Poid = P.Poid
                    AND A.Levelid = PE.Levelid
                    AND PE.Locationid LIKE '%'
            )
            AND PS.Levelid IN (
                SELECT TOP 1 PS.Levelid
                FROM Invent_POApprovalLevels PE (NOLOCK)
                INNER JOIN Invent_POApprovalEmpLevel PS (NOLOCK)
                    ON PE.levelid = PS.Levelid
                WHERE
                    PE.Locationid LIKE '%'
                    AND PS.Empid NOT IN (
                        SELECT DISTINCT Empid
                        FROM Invent_POApprovedEmployee A
                        WHERE A.Poid = P.Poid
                            AND A.Levelid = PE.Levelid
                            AND PE.Locationid LIKE '%'
                    )
                    AND PoType = P.AddnlParameter
                    AND PS.Levelid NOT IN (
                        SELECT DISTINCT Levelid
                        FROM Invent_POApprovedEmployee A
                        WHERE A.Poid = P.Poid
                            AND A.Levelid = PE.Levelid
                            AND PE.Locationid LIKE '%'
                    )
                ORDER BY PE.MinLimit, PE.MaxLimit
            )
    ) A
    ON A.Poid = d.Poid
WHERE
    d.Addnlparameter LIKE '%'
    AND d.Locationid LIKE '%'
    AND d.PoStatus = 'Approval Pending'

UNION ALL

SELECT
    t1.Location,
    t1.poid,
    t1.pono,
    t1.podate,
    t1.addnlparameter,
    t1.gStrSupplierDisp,
    t1.gStrMatDisp,
    NULL AS gStrGradeDisp,  -- Not available in second query
    t1.Ord_Qty,
    t1.Uom,
    t1.Rate,
    NULL AS Currency,  -- Not available in second query
    NULL AS price,  -- Not available in second query
    t1.povalue,
    t1.RawmatId,
    NULL AS TotalPoValue,  -- Not available in second query
    t1.empname AS POCreatedBY,  -- Changed from pocreatedby to empname
    t1.ServiceHSNID AS HSNID,
    t1.HSNCode,
    NULL AS Terms,  -- Not available in second query
    NULL AS QuotTerms,  -- Not available in second query
    t2.maxlimit,
    t2.MinLimit,
    t1.subcondesc,
    t1.ServiceHSNID,
    t1.TotalSubconPurchaseValue,
    t1.ActCostCentreDisplay AS gstrCostCentreDisplay  -- Changed from gstrCostCentreDisplay to ActCostCentreDisplay
FROM  
    (SELECT 
        com.Location,
        sc.supcode,
        sc.supid,
        sc.supname, 
        iscp.addnlparameter,
        iscp.postatus,
        iscp.LocationId,
        iscp.poid,
        iscp.Pono, 
        iscp.Podate, 
        ispp.Ord_Qty, 
        ispp.UOM, 
        ispp.rate, 
        round((ispp.Ord_Qty * ((ispp.Rate + ispp.pack)-Discount)), 2) as povalue, 
        ispp.Poproductid, 
        r.rawmatcode, 
        r.rawmatname, 
        ispp.subcondesc,
        R.RawmatId,
        empname,  -- This is the correct column name
        ispp.ServiceHSNID,
        iscp.TotalSubconPurchaseValue, 
        GH.HSNCode,
        Act.CostCentre + ' | ' + Act.Description AS ActCostCentreDisplay,  -- This is the correct column name
        sc.supname + ' | ' + sc.supcode AS gStrSupplierDisp,
        r.rawmatcode + ' | ' + r.rawmatname AS gStrMatDisp
    FROM 
        invent_supplier sc 
        INNER JOIN invent_subconpurchase iscp ON sc.supid=iscp.subconid
        INNER JOIN company com ON com.CompanyID=iscp.locationid
        INNER JOIN Employee e ON e.EmpId = iscp.entryempid 
        INNER JOIN invent_subconpurchaseproduct ispp ON iscp.poid= ispp.poid 
        LEFT OUTER JOIN rawmaterial r ON ispp.rawmatId=r.rawmatId 
        LEFT OUTER JOIN GST_HSN_SAC_NO GH ON GH.Hsn_Sac_ID = ispp.ServiceHSNID 
        LEFT OUTER JOIN IcSoftLedger.dbo.Acc_CostCentre Act ON iscp.ProfitCentreId = Act.CostCentreId
    ) t1  
    INNER JOIN (
        SELECT DISTINCT 
            PS.EMpid,
            poid,
            MaxLimit,
            MinLimit 
        FROM 
            Invent_POApprovalLevels PE (Nolock) 
            INNER JOIN Invent_POApprovalEmpLevel PS (Nolock) ON PE.levelid = PS.Levelid  
            INNER JOIN invent_subconpurchase P ON P.GTotal = 0 
                OR (
                    CASE 
                        WHEN PE.IsBasic='N' 
                            THEN (P.GTotal + (SELECT ISNULL(SUM(Amount),0) FROM Invent_ServiceProdWiseTax T WHERE T.poid = P.Poid)) 
                            ELSE P.GTotal 
                    END * CASE WHEN P.Exrate = 0 THEN 1 ELSE P.Exrate END <= (
                        SELECT MAX(Maxlimit) 
                        FROM Invent_POApprovalLevels 
                        WHERE Potype = P.AddnlParameter 
                        AND Locationid LIKE '%'
                    )
                )  
                AND (
                    CASE 
                        WHEN PE.IsBasic='N' 
                            THEN (P.GTotal + (SELECT ISNULL(SUM(Amount),0) FROM Invent_ServiceProdWiseTax T WHERE T.poid = P.Poid)) 
                            ELSE P.GTotal 
                    END * CASE WHEN P.Exrate = 0 THEN 1 ELSE P.Exrate END >= PE.Minlimit
                )  
        WHERE 
            postatus IN ('Approval Pending')  
            AND PE.POType = P.AddnlParameter  
            AND PE.LocationId LIKE '%'  
            AND PE.Levelid NOT IN (
                SELECT DISTINCT Levelid   
                FROM Invent_POApprovedEmployee A  
                WHERE A.Poid = P.POiD 
                AND A.levelid = PE.Levelid 
                AND PE.LocationId = 3
            )  
            AND PS.Levelid IN (
                SELECT TOP 1 PS.Levelid 
                FROM Invent_POApprovalLevels PE (Nolock)  
                INNER JOIN Invent_POApprovalEmpLevel PS (Nolock) ON PE.levelid = PS.Levelid  
                WHERE 
                    PS.Levelid = PE.levelid  
                    AND PE.Locationid LIKE '%'  
                    AND EMpid NOT IN (
                        SELECT DISTINCT Empid    
                        FROM Invent_POApprovedEmployee A  
                        WHERE A.Poid = P.POiD 
                        AND A.levelid = PE.Levelid 
                        AND PE.Locationid LIKE '%'
                    ) 
                    AND PoType = P.Addnlparameter  
                    AND PS.Levelid NOT IN (
                        SELECT DISTINCT Levelid 
                        FROM Invent_POApprovedEmployee A  
                        WHERE A.Poid = P.POiD 
                        AND A.levelid = PE.Levelid 
                        AND PE.Locationid LIKE '%'
                    ) 
                ORDER BY PE.MinLimit, PE.MaxLimit
            )
    ) t2 ON t1.Poid = T2.poid  
WHERE 
    t1.Addnlparameter = 'General PO Service' 
    AND t1.locationid LIKE '%' 
    AND t1.PoStatus = 'Approval Pending'
ORDER BY
    PoDate,
    PoNo,
    PoId;

-- Query to get distinct Purchase Types (Addnlparameter values)
SELECT DISTINCT Addnlparameter AS PurchaseType
FROM (
    -- First query's source
    SELECT Addnlparameter FROM Invent_PoDetails_Query 
    WHERE PoStatus = 'Approval Pending'
    
    UNION
    
    -- Second query's source
    SELECT Addnlparameter FROM invent_subconpurchase
    WHERE PoStatus = 'Approval Pending'
) AS CombinedPurchaseTypes
ORDER BY Addnlparameter;

