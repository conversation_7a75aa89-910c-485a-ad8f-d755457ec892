-- ULTRA SIMPLE Service PO Query - NO APPROVAL LOGIC
-- Just get all pending Service POs
USE Icsoft;

SELECT
    com.Location,
    iscp.poid,
    iscp.Pono,
    iscp.Podate,
    iscp.PoStatus AS postatus,
    iscp.addnlparameter,
    sc.supname + ' | ' + sc.supcode AS gStrSupplierDisp,
    ISNULL(r.rawmatcode + ' | ' + r.rawmatname, 'Service Item') AS gStrMatDisp,
    '-' AS grade,
    ISNULL(ispp.Ord_Qty, 1) AS Ord_Qty,
    ISNULL(ispp.UOM, 'Service') AS Uom,
    ISNULL(ispp.rate, 0) AS Rate,
    'INR' AS Currency,
    ISNULL(ispp.rate, 0) AS Price,
    ISNULL(ispp.rate * ispp.Ord_Qty, 0) AS povalue,
    ISNULL(ispp.RawmatId, 0) AS RawmatId,
    ISNULL(iscp.TotalSubconPurchaseValue, 0) AS TotalSubconPurchaseValue,
    e.empname AS pocreatedby,
    NULL AS ServiceHSNID,
    NULL AS HSNCode,
    NULL AS Terms
FROM
    invent_subconpurchase iscp
    INNER JOIN invent_supplier sc ON sc.supid = iscp.subconid
    INNER JOIN company com ON com.CompanyID = iscp.locationid
    INNER JOIN Employee e ON e.EmpId = iscp.entryempid
    LEFT OUTER JOIN invent_subconpurchaseproduct ispp ON iscp.poid = ispp.poid
    LEFT OUTER JOIN rawmaterial r ON ispp.rawmatId = r.rawmatId
WHERE
    iscp.PoStatus = 'Approval Pending'
ORDER BY
    iscp.PoDate DESC;
