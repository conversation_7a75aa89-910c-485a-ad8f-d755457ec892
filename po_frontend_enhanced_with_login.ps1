# Enhanced PO Approval Frontend with Login - Modern UI Version
param([int]$Port = 8090)

# Import database service with full path
. "C:\Users\<USER>\Documents\augment-projects\PO APPROVAL\database_service.ps1"

Write-Host "Starting Enhanced PO Approval Frontend with Login" -ForegroundColor Magenta
Write-Host "Port: $Port" -ForegroundColor Cyan

# Test database
$dbTest = Test-DatabaseConnection
if (-not $dbTest) {
    Write-Host "Database connection failed!" -ForegroundColor Red
    exit 1
}

# Configure firewall
netsh advfirewall firewall delete rule name="PO Frontend Enhanced" 2>$null
netsh advfirewall firewall add rule name="PO Frontend Enhanced" dir=in action=allow protocol=TCP localport=$Port

# Session management
$activeSessions = @{}

# Function to generate session token
function New-SessionToken {
    return [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes([System.Guid]::NewGuid().ToString()))
}

# Function to validate user credentials with better logging
function Test-UserCredentials {
    param($Username, $Password)
    try {
        Write-Host "Attempting login for user: $Username" -ForegroundColor Cyan
        $connection = Get-DatabaseConnection
        $query = @"
            SELECT EmpCode, EmpName, EmpId 
            FROM API_Employee 
            WHERE EmpCode = @Username 
            AND Password = @Password 
            AND IsActive = 1
"@
        Write-Host "Executing login query..." -ForegroundColor Cyan
        $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
        $command.Parameters.AddWithValue("@Username", $Username) | Out-Null
        $command.Parameters.AddWithValue("@Password", $Password) | Out-Null
        
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
        $dataTable = New-Object System.Data.DataTable
        $adapter.Fill($dataTable)
        $connection.Close()

        Write-Host "Query executed. Found $($dataTable.Rows.Count) matching rows." -ForegroundColor Cyan

        if ($dataTable.Rows.Count -eq 1) {
            Write-Host "Login successful for user: $Username" -ForegroundColor Green
            return @{
                Success = $true
                EmpCode = $dataTable.Rows[0]["EmpCode"]
                EmpName = $dataTable.Rows[0]["EmpName"]
                EmpId = $dataTable.Rows[0]["EmpId"]
            }
        }
        Write-Host "Login failed: Invalid credentials for user: $Username" -ForegroundColor Yellow
        return @{ Success = $false }
    }
    catch {
        Write-Host "Error validating credentials: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Stack trace: $($_.Exception.StackTrace)" -ForegroundColor Red
        return @{ Success = $false }
    }
}

# Enhanced JSON response function with CORS and compression support
function Send-JSON {
    param($Response, $Data)
    $Response.ContentType = "application/json"
    $Response.Headers.Add("Access-Control-Allow-Origin", "*")
    $Response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
    $Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type, Authorization")
    $Response.Headers.Add("X-Content-Type-Options", "nosniff")
    $Response.Headers.Add("X-Frame-Options", "DENY")
    $Response.Headers.Add("Content-Security-Policy", "default-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; img-src 'self' data:;")
    
    $json = $Data | ConvertTo-Json -Depth 10 -Compress
    $buffer = [System.Text.Encoding]::UTF8.GetBytes($json)
    $Response.ContentLength64 = $buffer.Length
    $Response.OutputStream.Write($buffer, 0, $buffer.Length)
    $Response.OutputStream.Close()
}

# Function to validate session
function Test-Session {
    param($Request)
    $authHeader = $Request.Headers["Authorization"]
    if (-not $authHeader) { return $false }
    
    $token = $authHeader -replace "Bearer ", ""
    return $activeSessions.ContainsKey($token)
}

# Create HTML template with login form and modern UI
$htmlTemplate = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced PO Approval System</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .loading { @apply animate-pulse bg-gray-200; }
        .btn-primary { @apply px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors; }
        .btn-secondary { @apply px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors; }
        .card { @apply bg-white rounded-lg shadow-lg p-6 mb-4; }
        .input-field { @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500; }
        .form-label { @apply block text-sm font-medium text-gray-700; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Login Form -->
    <div id="loginForm" class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    PO Approval System
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    Please sign in to continue
                </p>
            </div>
            <form id="loginFormElement" class="mt-8 space-y-6">
                <div class="rounded-md shadow-sm -space-y-px">
                    <div>
                        <label for="username" class="sr-only">Username</label>
                        <input id="username" name="username" type="text" required 
                            class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                            placeholder="Username">
                    </div>
                    <div>
                        <label for="password" class="sr-only">Password</label>
                        <input id="password" name="password" type="password" required 
                            class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                            placeholder="Password">
                    </div>
                </div>

                <div>
                    <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fas fa-sign-in-alt"></i>
                        </span>
                        Sign in
                    </button>
                </div>
                <div id="loginError" class="text-red-600 text-center hidden"></div>
            </form>
        </div>
    </div>

    <!-- Main Application (hidden by default) -->
    <div id="mainApp" class="hidden">
        <nav class="bg-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <span class="text-xl font-semibold text-gray-800">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            PO Approval System
                        </span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span id="userInfo" class="text-gray-600">
                            <i class="fas fa-user-circle mr-2"></i>
                            <span id="userName"></span>
                        </span>
                        <button id="refreshBtn" class="btn-secondary">
                            <i class="fas fa-sync-alt mr-2"></i>
                            Refresh
                        </button>
                        <button onclick="handleLogout()" class="btn-secondary">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Logout
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <main class="max-w-7xl mx-auto px-4 py-8">
            <div class="filter-section">
                <div class="flex-1">
                    <label class="block text-sm font-medium text-gray-700">Purchase Type</label>
                    <select id="purchaseTypeFilter" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <option value="">All Types</option>
                    </select>
                </div>
                <div class="flex-1">
                    <label class="block text-sm font-medium text-gray-700">Date Range</label>
                    <div class="flex space-x-2">
                        <input type="date" id="startDate" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <input type="date" id="endDate" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th class="table-header">PO No</th>
                                <th class="table-header">Date</th>
                                <th class="table-header">Type</th>
                                <th class="table-header">Supplier</th>
                                <th class="table-header">Material</th>
                                <th class="table-header">Quantity</th>
                                <th class="table-header">Value</th>
                                <th class="table-header">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="poTable" class="divide-y divide-gray-200">
                            <!-- Data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Define the API base URL
        const API_BASE_URL = 'http://localhost:$Port';
        console.log('API Base URL:', API_BASE_URL);

        // Get form elements
        const loginForm = document.getElementById('loginFormElement');
        const loginError = document.getElementById('loginError');

        // Handle form submission
        loginForm.addEventListener('submit', async (event) => {
            event.preventDefault();
            console.log('Login form submitted');

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                console.log('Sending login request...');
                const response = await fetch(`\${API_BASE_URL}/api/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                console.log('Response received:', response.status);
                const data = await response.json();
                console.log('Response data:', data);

                if (data.success) {
                    // Store auth data
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('userData', JSON.stringify(data.user));
                    
                    // Update UI
                    document.getElementById('userName').textContent = data.user.name;
                    document.getElementById('loginForm').classList.add('hidden');
                    document.getElementById('mainApp').classList.remove('hidden');
                    
                    // Load initial data
                    fetchPurchaseTypes();
                    fetchPendingPOs();
                } else {
                    loginError.textContent = data.message || 'Invalid credentials';
                    loginError.classList.remove('hidden');
                }
            } catch (error) {
                console.error('Login error:', error);
                loginError.textContent = 'An error occurred during login. Please try again.';
                loginError.classList.remove('hidden');
            }
        });

        // Rest of your JavaScript code...
    </script>
</body>
</html>
"@

# Create the HTTP listener
$listener = New-Object System.Net.HttpListener
$listener.Prefixes.Add("http://localhost:$Port/")
$listener.Start()

Write-Host "Server is running on http://localhost:$Port/" -ForegroundColor Green

while ($listener.IsListening) {
    $context = $listener.GetContext()
    $request = $context.Request
    $response = $context.Response

    $path = $request.Url.LocalPath
    Write-Host "[$($request.HttpMethod)] $path" -ForegroundColor Cyan

    # Handle CORS preflight requests
    if ($request.HttpMethod -eq 'OPTIONS') {
        $response.Headers.Add("Access-Control-Allow-Origin", "*")
        $response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
        $response.Headers.Add("Access-Control-Allow-Headers", "Content-Type, Authorization")
        $response.StatusCode = 200
        $response.Close()
        continue
    }

    switch -regex ($path) {
        '^/$' {
            # Check for URL parameters first
            $username = $request.QueryString["username"]
            $password = $request.QueryString["password"]
            
            if ($username -and $password) {
                Write-Host "Attempting login with URL parameters for user: $username" -ForegroundColor Cyan
                $authResult = Test-UserCredentials -Username $username -Password $password
                if ($authResult.Success) {
                    $token = New-SessionToken
                    $activeSessions[$token] = @{
                        Username = $username
                        EmpName = $authResult.EmpName
                        EmpId = $authResult.EmpId
                        LastAccess = Get-Date
                    }
                    
                    # Modify the HTML to auto-login
                    $autoLoginScript = @"
<script>
    // Store auth data immediately
    localStorage.setItem('authToken', '$token');
    localStorage.setItem('userData', JSON.stringify({
        username: '$username',
        name: '$($authResult.EmpName)',
        empId: '$($authResult.EmpId)'
    }));
    
    // Hide login form and show main app on load
    window.addEventListener('DOMContentLoaded', () => {
        document.getElementById('userName').textContent = '$($authResult.EmpName)';
        document.getElementById('loginForm').classList.add('hidden');
        document.getElementById('mainApp').classList.remove('hidden');
        // Load initial data
        fetchPurchaseTypes();
        fetchPendingPOs();
    });
</script>
"@
                    $htmlWithJs = $htmlTemplate -replace '</head>', "$autoLoginScript</head>"
                } else {
                    $htmlWithJs = $htmlTemplate -replace '</head>', "<script>window.addEventListener('DOMContentLoaded', () => { document.getElementById('loginError').textContent = 'Invalid credentials'; document.getElementById('loginError').classList.remove('hidden'); });</script></head>"
                }
            } else {
                $htmlWithJs = $htmlTemplate
            }
            
            $response.ContentType = "text/html"
            $buffer = [System.Text.Encoding]::UTF8.GetBytes($htmlWithJs)
            $response.ContentLength64 = $buffer.Length
            $response.OutputStream.Write($buffer, 0, $buffer.Length)
            $response.OutputStream.Close()
        }
        
        '^/api/login$' {
            if ($request.HttpMethod -ne 'POST') {
                Write-Host "Invalid method for login: $($request.HttpMethod)" -ForegroundColor Red
                $response.StatusCode = 405
                $response.Close()
                continue
            }

            Write-Host "Processing login request..." -ForegroundColor Cyan
            $reader = New-Object System.IO.StreamReader($request.InputStream)
            $body = $reader.ReadToEnd()
            Write-Host "Received login request body: $body" -ForegroundColor Cyan
            
            try {
                $credentials = $body | ConvertFrom-Json
                Write-Host "Parsed credentials for username: $($credentials.username)" -ForegroundColor Cyan

                $authResult = Test-UserCredentials -Username $credentials.username -Password $credentials.password
                if ($authResult.Success) {
                    $token = New-SessionToken
                    $activeSessions[$token] = @{
                        Username = $credentials.username
                        EmpName = $authResult.EmpName
                        EmpId = $authResult.EmpId
                        LastAccess = Get-Date
                    }

                    Write-Host "Login successful. Generated token: $token" -ForegroundColor Green
                    Send-JSON -Response $response -Data @{
                        success = $true
                        token = $token
                        user = @{
                            username = $credentials.username
                            name = $authResult.EmpName
                            empId = $authResult.EmpId
                        }
                    }
                } else {
                    Write-Host "Login failed for username: $($credentials.username)" -ForegroundColor Yellow
                    Send-JSON -Response $response -Data @{
                        success = $false
                        message = "Invalid credentials"
                    }
                }
            }
            catch {
                Write-Host "Error processing login request: $($_.Exception.Message)" -ForegroundColor Red
                Write-Host "Stack trace: $($_.Exception.StackTrace)" -ForegroundColor Red
                Send-JSON -Response $response -Data @{
                    success = $false
                    message = "Error processing login request"
                }
            }
        }

        '^/api/purchase-types$' {
            if (-not (Test-Session -Request $request)) {
                $response.StatusCode = 401
                $response.Close()
                continue
            }
            $types = Get-PurchaseTypes
            Send-JSON -Response $response -Data $types
        }
        
        '^/api/pending-pos$' {
            if (-not (Test-Session -Request $request)) {
                $response.StatusCode = 401
                $response.Close()
                continue
            }
            $purchaseType = $request.QueryString["type"]
            if ([string]::IsNullOrEmpty($purchaseType)) {
                $purchaseType = '%'
            }
            $pos = Get-PendingPOs -PurchaseType $purchaseType
            Send-JSON -Response $response -Data $pos
        }
        
        default {
            $response.StatusCode = 404
            $response.Close()
        }
    }
} 