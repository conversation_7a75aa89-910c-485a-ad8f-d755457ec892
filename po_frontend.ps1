# Simple PO Approval Frontend - Original Working Version
param([int]$Port = 8089)

# Import database service with full path
. "C:\Users\<USER>\Documents\augment-projects\PO APPROVAL\database_service.ps1"

Write-Host "🚀 Starting PO Approval Frontend" -ForegroundColor Green
Write-Host "Port: $Port" -ForegroundColor Cyan

# Test database
$dbTest = Test-DatabaseConnection
if (-not $dbTest) {
    Write-Host "Database connection failed!" -ForegroundColor Red
    exit 1
}

# Ensure API_Employee table exists
function Ensure-APIEmployeeTable {
    try {
        $connection = Get-DatabaseConnection
        $checkTableQuery = @"
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='API_Employee' AND xtype='U')
            BEGIN
                CREATE TABLE API_Employee (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    EmpCode NVARCHAR(50) NOT NULL UNIQUE,
                    EmpName NVARCHAR(100),
                    EmpId INT,
                    Password NVARCHAR(100) NOT NULL,
                    CreatedDate DATETIME DEFAULT GETDATE(),
                    ModifiedDate DATETIME DEFAULT GETDATE(),
                    CreatedBy NVARCHAR(50),
                    ModifiedBy NVARCHAR(50),
                    LastLoginDate DATETIME,
                    IsActive BIT DEFAULT 1
                )

                PRINT 'API_Employee table created successfully'
            END
            ELSE
            BEGIN
                PRINT 'API_Employee table already exists'
            END
"@
        $command = New-Object System.Data.SqlClient.SqlCommand($checkTableQuery, $connection)
        $command.ExecuteNonQuery()
        $connection.Close()
        Write-Host "API_Employee table verified" -ForegroundColor Green
    }
    catch {
        Write-Host "Error creating API_Employee table: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

Ensure-APIEmployeeTable

# Configure firewall
netsh advfirewall firewall delete rule name="PO Frontend" 2>$null
netsh advfirewall firewall add rule name="PO Frontend" dir=in action=allow protocol=TCP localport=$Port

# JSON response function
function Send-JSON {
    param($Response, $Data)
    $Response.ContentType = "application/json"
    $Response.Headers.Add("Access-Control-Allow-Origin", "*")
    $json = $Data | ConvertTo-Json -Depth 10
    $buffer = [System.Text.Encoding]::UTF8.GetBytes($json)
    $Response.ContentLength64 = $buffer.Length
    $Response.OutputStream.Write($buffer, 0, $buffer.Length)
    $Response.OutputStream.Close()
}

# Add this function to get distinct purchase types
function Get-PurchaseTypes {
    try {
        $connection = Get-DatabaseConnection
        if (-not $connection) { return @() }

        $query = @"
SELECT DISTINCT Addnlparameter AS PurchaseType
FROM (
    -- First query's source
    SELECT Addnlparameter FROM Invent_PoDetails_Query
    WHERE PoStatus = 'Approval Pending'

    UNION

    -- Second query's source
    SELECT Addnlparameter FROM invent_subconpurchase
    WHERE PoStatus = 'Approval Pending'
) AS CombinedPurchaseTypes
ORDER BY Addnlparameter;
"@

        $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
        $dataTable = New-Object System.Data.DataTable
        $adapter.Fill($dataTable)
        $connection.Close()

        $purchaseTypes = @()
        foreach ($row in $dataTable.Rows) {
            $purchaseTypes += $row["PurchaseType"]
        }
        return $purchaseTypes
    }
    catch {
        Write-Host "Error getting purchase types: $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

# Add this function for testing
function Test-BasicPOQuery {
    try {
        $connection = Get-DatabaseConnection
        $query = "SELECT TOP 10 * FROM Invent_purchase WHERE 1=1"
        $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
        $dataTable = New-Object System.Data.DataTable
        $adapter.Fill($dataTable)
        $connection.Close()

        Write-Host "Basic query returned $($dataTable.Rows.Count) rows" -ForegroundColor Green
        return $dataTable.Rows.Count -gt 0
    }
    catch {
        Write-Host "Error in basic query: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Add this function to check table structure
function Test-TableStructure {
    try {
        $connection = Get-DatabaseConnection
        $query = "SELECT TOP 0 * FROM Invent_PoDetails_Query"
        $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
        $dataTable = New-Object System.Data.DataTable
        $adapter.Fill($dataTable)

        Write-Host "Table structure check:" -ForegroundColor Cyan
        foreach ($column in $dataTable.Columns) {
            Write-Host "  - $($column.ColumnName): $($column.DataType)" -ForegroundColor Cyan
        }

        $connection.Close()
        return $true
    }
    catch {
        Write-Host "Error checking table structure: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Call this after database connection test
Test-TableStructure

# Add this to the beginning of the script after database connection test
Write-Host "Testing basic PO query..." -ForegroundColor Cyan
$basicTest = Test-BasicPOQuery
Write-Host "Basic PO query test result: $basicTest" -ForegroundColor Cyan

# Also test if there are any pending POs specifically
try {
    $connection = Get-DatabaseConnection
    $query = "SELECT COUNT(*) FROM Invent_purchase WHERE postatus = 'Approval Pending'"
    $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
    $count = $command.ExecuteScalar()
    $connection.Close()
    Write-Host "Found $count POs with 'Approval Pending' status" -ForegroundColor Cyan
} catch {
    Write-Host "Error checking pending POs: $($_.Exception.Message)" -ForegroundColor Red
}

# Modify the Get-PendingPOs function to accept a purchase type filter
function Get-PendingPOs {
    param(
        [string]$PurchaseType = '%'
    )

    try {
        $connection = Get-DatabaseConnection
        if (-not $connection) {
            Write-Host "Database connection failed in Get-PendingPOs" -ForegroundColor Red
            return @()
        }

        Write-Host "Executing query with PurchaseType filter: $PurchaseType" -ForegroundColor Cyan
        $query = @"
-- Combined PO Approval Query with UNION (Fixed version)
SELECT
    COM.LOCATION,
    d.PoId,
    d.PoNo,
    d.PoDate,
    d.Addnlparameter,
    SupName + ' | ' + SupCode AS gStrSupplierDisp,
    RawMatCode + ' | ' + RawMatName AS gStrMatDisp,
    GCode + ' | ' + Gradename AS gStrGradeDisp,
    d.Ord_Qty,
    d.Uom,
    ROUND(d.Rate, 4) AS Rate,
    d.CurrCode AS Currency,
    ((d.Rate * d.ExRate + Pack) - Discount) AS price,
    ROUND((d.Ord_Qty * ((d.Rate + d.Pack) - d.Discount)), 2) AS povalue,
    d.RawMatId,
    d.TotalPoValue,
    d.EMPNAME AS POCreatedBY,
    d.HSNID,
    GH.HSNCode,
    Terms,
    QuotTerms,
    NULL AS MaxLimit,  -- Added for second query compatibility
    NULL AS MinLimit,  -- Added for second query compatibility
    NULL AS subcondesc,  -- Added for second query compatibility
    NULL AS ServiceHSNID,  -- Added for second query compatibility
    NULL AS TotalSubconPurchaseValue,  -- Added for second query compatibility
    NULL AS gstrCostCentreDisplay  -- Added for second query compatibility
FROM
    Invent_PoDetails_Query d
    LEFT OUTER JOIN IcSoftLedger.dbo.Acc_CostCentre Act
        ON d.ProfitCentreId = Act.CostCentreId
    LEFT OUTER JOIN GST_HSN_SAC_NO GH
        ON GH.Hsn_Sac_ID = d.HSNID
    INNER JOIN COMPANY COM ON COM.CompanyID = D.LocationID
    INNER JOIN (
        SELECT DISTINCT
            PS.Empid,
            P.Poid,
            PE.Minlimit,
            PE.Maxlimit
        FROM
            Invent_POApprovalLevels PE (NOLOCK)
            INNER JOIN Invent_POApprovalEmpLevel PS (NOLOCK)
                ON PE.levelid = PS.Levelid
            INNER JOIN Invent_PoDetails_Query P
                ON (
                    P.GTotal = 0
                    AND PE.Locationid LIKE '%'
                )
                OR (
                    (
                        CASE
                            WHEN PE.IsBasic = 'N'
                                THEN (P.GTotal + ISNULL((SELECT SUM(Amount) FROM Invent_PoprodWiseTax T WHERE T.Poid = P.Poid), 0))
                                ELSE P.GTotal
                        END * P.Exrate
                    ) <= (
                        SELECT MAX(Maxlimit)
                        FROM Invent_POApprovalLevels
                        WHERE Potype = P.AddnlParameter
                            AND Locationid LIKE '%'
                    )
                    AND (
                        CASE
                            WHEN PE.IsBasic = 'N'
                                THEN (P.GTotal + ISNULL((SELECT SUM(Amount) FROM Invent_PoprodWiseTax T WHERE T.Poid = P.Poid), 0))
                                ELSE P.GTotal
                        END * P.Exrate
                    ) >= PE.Minlimit
                )
        WHERE
            P.Postatus = 'Approval Pending'
            AND PS.Empid LIKE '%'
            AND PE.POType = P.AddnlParameter
            AND PE.Locationid LIKE '%'
            AND PE.Levelid NOT IN (
                SELECT DISTINCT Levelid
                FROM Invent_POApprovedEmployee A
                WHERE A.Poid = P.Poid
                    AND A.Levelid = PE.Levelid
                    AND PE.Locationid LIKE '%'
            )
            AND PS.Levelid IN (
                SELECT TOP 1 PS.Levelid
                FROM Invent_POApprovalLevels PE (NOLOCK)
                INNER JOIN Invent_POApprovalEmpLevel PS (NOLOCK)
                    ON PE.levelid = PS.Levelid
                WHERE
                    PE.Locationid LIKE '%'
                    AND PS.Empid NOT IN (
                        SELECT DISTINCT Empid
                        FROM Invent_POApprovedEmployee A
                        WHERE A.Poid = P.Poid
                            AND A.Levelid = PE.Levelid
                            AND PE.Locationid LIKE '%'
                    )
                    AND PoType = P.AddnlParameter
                    AND PS.Levelid NOT IN (
                        SELECT DISTINCT Levelid
                        FROM Invent_POApprovedEmployee A
                        WHERE A.Poid = P.Poid
                            AND A.Levelid = PE.Levelid
                            AND PE.Locationid LIKE '%'
                    )
                ORDER BY PE.MinLimit, PE.MaxLimit
            )
    ) A
    ON A.Poid = d.Poid
WHERE
    d.Addnlparameter LIKE '$PurchaseType'
    AND d.Locationid LIKE '%'
    AND d.PoStatus = 'Approval Pending'

UNION ALL

SELECT
    t1.Location,
    t1.poid,
    t1.pono,
    t1.podate,
    t1.addnlparameter,
    t1.gStrSupplierDisp,
    t1.gStrMatDisp,
    NULL AS gStrGradeDisp,
    t1.Ord_Qty,
    t1.Uom,
    t1.Rate,
    NULL AS Currency,
    NULL AS price,
    t1.povalue,
    t1.RawmatId,
    NULL AS TotalPoValue,
    t1.empname AS POCreatedBY,
    t1.ServiceHSNID AS HSNID,
    t1.HSNCode,
    NULL AS Terms,
    NULL AS QuotTerms,
    t2.maxlimit,
    t2.MinLimit,
    t1.subcondesc,
    t1.ServiceHSNID,
    t1.TotalSubconPurchaseValue,
    t1.ActCostCentreDisplay AS gstrCostCentreDisplay
FROM
    (SELECT
        com.Location,
        sc.supcode,
        sc.supid,
        sc.supname,
        iscp.addnlparameter,
        iscp.postatus,
        iscp.LocationId,
        iscp.poid,
        iscp.Pono,
        iscp.Podate,
        ispp.Ord_Qty,
        ispp.UOM,
        ispp.rate,
        round((ispp.Ord_Qty * ((ispp.Rate + ispp.pack)-Discount)), 2) as povalue,
        ispp.Poproductid,
        r.rawmatcode,
        r.rawmatname,
        ispp.subcondesc,
        R.RawmatId,
        empname,  -- This is the correct column name
        ispp.ServiceHSNID,
        iscp.TotalSubconPurchaseValue,
        GH.HSNCode,
        Act.CostCentre + ' | ' + Act.Description AS ActCostCentreDisplay,  -- This is the correct column name
        sc.supname + ' | ' + sc.supcode AS gStrSupplierDisp,
        r.rawmatcode + ' | ' + r.rawmatname AS gStrMatDisp
    FROM
        invent_supplier sc
        INNER JOIN invent_subconpurchase iscp ON sc.supid=iscp.subconid
        INNER JOIN company com ON com.CompanyID=iscp.locationid
        INNER JOIN Employee e ON e.EmpId = iscp.entryempid
        INNER JOIN invent_subconpurchaseproduct ispp ON iscp.poid= ispp.poid
        LEFT OUTER JOIN rawmaterial r ON ispp.rawmatId=r.rawmatId
        LEFT OUTER JOIN GST_HSN_SAC_NO GH ON GH.Hsn_Sac_ID = ispp.ServiceHSNID
        LEFT OUTER JOIN IcSoftLedger.dbo.Acc_CostCentre Act ON iscp.ProfitCentreId = Act.CostCentreId
    ) t1
    INNER JOIN (
        SELECT DISTINCT
            PS.EMpid,
            poid,
            MaxLimit,
            MinLimit
        FROM
            Invent_POApprovalLevels PE (Nolock)
            INNER JOIN Invent_POApprovalEmpLevel PS (Nolock) ON PE.levelid = PS.Levelid
            INNER JOIN invent_subconpurchase P ON P.GTotal = 0
                OR (
                    CASE
                        WHEN PE.IsBasic='N'
                            THEN (P.GTotal + (SELECT ISNULL(SUM(Amount),0) FROM Invent_ServiceProdWiseTax T WHERE T.poid = P.Poid))
                            ELSE P.GTotal
                    END * CASE WHEN P.Exrate = 0 THEN 1 ELSE P.Exrate END <= (
                        SELECT MAX(Maxlimit)
                        FROM Invent_POApprovalLevels
                        WHERE Potype = P.AddnlParameter
                        AND Locationid LIKE '%'
                    )
                )
                AND (
                    CASE
                        WHEN PE.IsBasic='N'
                            THEN (P.GTotal + (SELECT ISNULL(SUM(Amount),0) FROM Invent_ServiceProdWiseTax T WHERE T.poid = P.Poid))
                            ELSE P.GTotal
                    END * CASE WHEN P.Exrate = 0 THEN 1 ELSE P.Exrate END >= PE.Minlimit
                )
        WHERE
            postatus IN ('Approval Pending')
            AND PE.POType = P.AddnlParameter
            AND PE.LocationId LIKE '%'
            AND PE.Levelid NOT IN (
                SELECT DISTINCT Levelid
                FROM Invent_POApprovedEmployee A
                WHERE A.Poid = P.POiD
                AND A.levelid = PE.Levelid
                AND PE.LocationId = 3
            )
            AND PS.Levelid IN (
                SELECT TOP 1 PS.Levelid
                FROM Invent_POApprovalLevels PE (Nolock)
                INNER JOIN Invent_POApprovalEmpLevel PS (Nolock) ON PE.levelid = PS.Levelid
                WHERE
                    PS.Levelid = PE.levelid
                    AND PE.Locationid LIKE '%'
                    AND EMpid NOT IN (
                        SELECT DISTINCT Empid
                        FROM Invent_POApprovedEmployee A
                        WHERE A.Poid = P.POiD
                        AND A.levelid = PE.Levelid
                        AND PE.Locationid LIKE '%'
                    )
                    AND PoType = P.Addnlparameter
                    AND PS.Levelid NOT IN (
                        SELECT DISTINCT Levelid
                        FROM Invent_POApprovedEmployee A
                        WHERE A.Poid = P.POiD
                        AND A.levelid = PE.Levelid
                        AND PE.Locationid LIKE '%'
                    )
                ORDER BY PE.MinLimit, PE.MaxLimit
            )
    ) t2 ON t1.Poid = T2.poid
WHERE
    t1.Addnlparameter LIKE '$PurchaseType'
    AND t1.locationid LIKE '%'
    AND t1.PoStatus = 'Approval Pending'
ORDER BY
    PoDate,
    PoNo,
    PoId
"@

        $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
        $dataTable = New-Object System.Data.DataTable
        $adapter.Fill($dataTable)
        $connection.Close()

        $results = @()
        foreach ($row in $dataTable.Rows) {
            $results += @{
                Location = $row["LOCATION"]
                PoId = $row["PoId"]
                PoNo = $row["PoNo"]
                PoDate = $row["PoDate"]
                POType = $row["POType"]
                Supplier = $row["Supplier"]
                Material = $row["Material"]
                Grade = $row["Grade"]
                Quantity = $row["Quantity"]
                UOM = $row["UOM"]
                Rate = $row["Rate"]
                Currency = $row["Currency"]
                POValue = $row["POValue"]
                TotalPOValue = $row["TotalPoValue"]
                CreatedBy = $row["CreatedBy"]
                HSNCode = $row["HSNCode"]
                Terms = $row["Terms"]
                QuotTerms = $row["QuotTerms"]
            }
        }

        Write-Host "Retrieved $($results.Count) line items from $(($results | Group-Object PoId).Count) pending POs" -ForegroundColor Green
        return $results
    }
    catch {
        Write-Host "Error getting pending POs: $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

# Get distinct PO totals
function Get-POTotals {
    try {
        $connection = Get-DatabaseConnection
        if (-not $connection) { return @{} }

        $query = @"
SELECT DISTINCT
    d.PoId,
    d.TotalPoValue
FROM Invent_PoDetails_Query d
WHERE d.PoStatus = 'Approval Pending'
"@

        $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
        $dataTable = New-Object System.Data.DataTable
        $adapter.Fill($dataTable)
        $connection.Close()

        $totals = @{}
        foreach ($row in $dataTable.Rows) {
            $poIdKey = [string]$row["PoId"]
            $totals[$poIdKey] = [double]$row["TotalPoValue"]
        }
        return $totals
    }
    catch {
        Write-Host "Error getting PO totals: $($_.Exception.Message)" -ForegroundColor Red
        return @{}
    }
}

# Get Material POs
function Get-MaterialPOs {
    try {
        $connection = Get-DatabaseConnection
        if (-not $connection) { return @() }

        Write-Host "Fetching Material POs..." -ForegroundColor Cyan

        $query = Get-Content "material_po_simple.sql" -Raw
        $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
        $dataTable = New-Object System.Data.DataTable
        $adapter.Fill($dataTable)
        $connection.Close()

        Write-Host "Material POs found: $($dataTable.Rows.Count)" -ForegroundColor Green

        $results = @()
        foreach ($row in $dataTable.Rows) {
            $results += @{
                PoId = $row["PoId"]
                PoNo = $row["PoNo"]
                PoDate = $row["PoDate"]
                PoStatus = $row["PoStatus"]  # Added PO Status
                Location = $row["LOCATION"]
                POType = $row["Addnlparameter"]
                Supplier = $row["gStrSupplierDisp"]
                Material = $row["gStrMatDisp"]
                Grade = $row["gStrGradeDisp"]
                Quantity = $row["Ord_Qty"]
                UOM = $row["Uom"]
                Rate = $row["Rate"]
                Currency = $row["Currency"]
                POValue = $row["povalue"]
                TotalPOValue = $row["TotalPoValue"]
                CreatedBy = $row["POCreatedBY"]
                HSNCode = $row["HSNCode"]
                Terms = $row["Terms"]
                QuotTerms = $row["QuotTerms"]
                POCategory = "Material"
            }
        }
        return $results
    }
    catch {
        Write-Host "Error fetching Material POs: $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

# Get Service POs
function Get-ServicePOs {
    try {
        $connection = Get-DatabaseConnection
        if (-not $connection) { return @() }

        Write-Host "Fetching Service POs..." -ForegroundColor Cyan

        $query = Get-Content "service_po_simple.sql" -Raw
        $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
        $dataTable = New-Object System.Data.DataTable
        $adapter.Fill($dataTable)
        $connection.Close()

        Write-Host "Service POs found: $($dataTable.Rows.Count)" -ForegroundColor Green

        $results = @()
        foreach ($row in $dataTable.Rows) {
            $results += @{
                PoId = $row["poid"]
                PoNo = $row["pono"]
                PoDate = $row["podate"]
                PoStatus = $row["postatus"]  # Added PO Status
                Location = $row["location"]
                POType = $row["addnlparameter"]
                Supplier = $row["gStrSupplierDisp"]
                Material = $row["gStrMatDisp"]
                Grade = $row["grade"]
                Quantity = $row["Ord_Qty"]
                UOM = $row["Uom"]
                Rate = $row["Rate"]
                Currency = $row["Currency"]
                POValue = $row["povalue"]
                TotalPOValue = $row["TotalSubconPurchaseValue"]
                CreatedBy = $row["pocreatedby"]
                HSNCode = $row["HSNCode"]
                Terms = $row["Terms"]
                QuotTerms = ""
                POCategory = "Service"
            }
        }
        return $results
    }
    catch {
        Write-Host "Error fetching Service POs: $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

# GROUPED POs - Group by PO Number
function Get-GroupedPOs {
    Write-Host "GROUPED POs: Getting POs and grouping by PO Number..." -ForegroundColor Yellow

    try {
        $connectionString = "Server=WIN-PRK-SRV-01;Database=Icsoft;Integrated Security=true;TrustServerCertificate=true;"
        $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
        $connection.Open()

        # Get all line items - SIMPLE VERSION THAT WAS WORKING + HSNCode
        $query = @"
        SELECT
            d.PoId,
            d.PoNo,
            d.PoDate,
            d.PoStatus,
            d.Addnlparameter,
            d.SupName,
            d.TotalPoValue,
            d.EMPNAME,
            COM.LOCATION,
            d.RawMatName,
            d.Gradename,
            d.Ord_Qty,
            d.UOM,
            d.Rate,
            ROUND((d.Ord_Qty * d.Rate), 2) AS LineValue,
            ISNULL(GH.HSNCode, 'N/A') AS HSNCode,
            d.Terms
        FROM Invent_PoDetails_Query d
        INNER JOIN COMPANY COM ON COM.CompanyID = D.LocationID
        LEFT OUTER JOIN GST_HSN_SAC_NO GH ON GH.Hsn_Sac_ID = d.HSNID
        WHERE d.PoStatus = 'Approval Pending'
            AND d.PoNo IS NOT NULL
            AND d.PoNo != ''
            AND d.PoNo != 'N/A'
        ORDER BY d.PoDate DESC, d.PoNo, d.PoProductId
"@

        $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
        $dataTable = New-Object System.Data.DataTable
        $adapter.Fill($dataTable)
        $connection.Close()

        Write-Host "Query returned: $($dataTable.Rows.Count) line items" -ForegroundColor Green

        # Group by PoId
        $grouped = @{}
        foreach ($row in $dataTable.Rows) {
            $poId = [string]$row["PoId"]
            $poNo = [string]$row["PoNo"]

            # DEBUG: Show what we're getting from database
            Write-Host "DEBUG: PoId=$poId, PoNo='$poNo', Type=$($poNo.GetType().Name)" -ForegroundColor Yellow

            # SKIP any PO with N/A, null, or empty PoNo
            if ([string]::IsNullOrWhiteSpace($poNo) -or $poNo -eq "N/A" -or $poNo -eq "" -or $poNo -eq $null -or $poNo -like "*N/A*") {
                Write-Host "SKIPPING PO with invalid PoNo: '$poNo'" -ForegroundColor Red
                continue
            }

            if (-not $grouped.ContainsKey($poId)) {
                # Create new PO group
                $grouped[$poId] = @{
                    PoId = $row["PoId"]
                    PoNo = $row["PoNo"]
                    PoDate = $row["PoDate"]
                    Location = $row["LOCATION"]
                    POType = $row["Addnlparameter"]
                    Supplier = $row["SupName"]
                    TotalValue = [double]$row["TotalPoValue"]
                    CreatedBy = $row["EMPNAME"]
                    POCategory = "Material"
                    Terms = if ($row["Terms"]) { $row["Terms"] } else { "" }
                    QuotTerms = ""
                    LineItems = @()
                    ItemCount = 0
                }
            }

            # Add line item to the PO
            $grouped[$poId].LineItems += @{
                Material = $row["RawMatName"]
                Grade = $row["Gradename"]
                Quantity = $row["Ord_Qty"]
                UOM = $row["UOM"]
                Rate = $row["Rate"]
                Amount = [double]$row["LineValue"]
                HSNCode = $row["HSNCode"]
            }
            $grouped[$poId].ItemCount++
        }

        $results = $grouped.Values
        Write-Host "Grouped into $($results.Count) unique POs" -ForegroundColor Green
        return $results

    } catch {
        Write-Host "GROUPED query error: $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

# Approve PO function - Updated to handle both Material and Service POs
function Approve-PO {
    param($PoId, $ApprovedBy)

    try {
        $connection = Get-DatabaseConnection

        # First, check if this is a Material PO (Invent_purchase table)
        $checkMaterialQuery = "SELECT COUNT(*) FROM Invent_purchase WHERE poid=$PoId"
        $checkCommand = New-Object System.Data.SqlClient.SqlCommand($checkMaterialQuery, $connection)
        $materialCount = $checkCommand.ExecuteScalar()

        if ($materialCount -gt 0) {
            # This is a Material PO - update Invent_purchase table
            Write-Host "Approving Material PO ID: $PoId" -ForegroundColor Cyan
            $query = "UPDATE Invent_purchase SET postatus='Acknowledged', ApprovedBy='$ApprovedBy' WHERE poid=$PoId"
        } else {
            # Check if this is a Service PO (invent_subconpurchase table)
            $checkServiceQuery = "SELECT COUNT(*) FROM invent_subconpurchase WHERE poid=$PoId"
            $checkServiceCommand = New-Object System.Data.SqlClient.SqlCommand($checkServiceQuery, $connection)
            $serviceCount = $checkServiceCommand.ExecuteScalar()

            if ($serviceCount -gt 0) {
                # This is a Service PO - update invent_subconpurchase table
                Write-Host "Approving Service PO ID: $PoId" -ForegroundColor Cyan
                $query = "UPDATE invent_subconpurchase SET postatus='Acknowledged', ApprovedBy='$ApprovedBy' WHERE poid=$PoId"
            } else {
                Write-Host "PO ID $PoId not found in either Material or Service PO tables" -ForegroundColor Red
                $connection.Close()
                return $false
            }
        }

        $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
        $result = $command.ExecuteNonQuery()
        $connection.Close()

        Write-Host "PO approval result: $result rows affected" -ForegroundColor Green
        return $result -gt 0
    }
    catch {
        Write-Host "Error approving PO: $($_.Exception.Message)" -ForegroundColor Red
        if ($connection.State -eq 'Open') { $connection.Close() }
        return $false
    }
}

# Reject PO function - Updated to handle both Material and Service POs
function Reject-PO {
    param($PoId, $RejectedBy)

    try {
        $connection = Get-DatabaseConnection

        # First, check if this is a Material PO (Invent_purchase table)
        $checkMaterialQuery = "SELECT COUNT(*) FROM Invent_purchase WHERE poid=$PoId"
        $checkCommand = New-Object System.Data.SqlClient.SqlCommand($checkMaterialQuery, $connection)
        $materialCount = $checkCommand.ExecuteScalar()

        if ($materialCount -gt 0) {
            # This is a Material PO - update Invent_purchase table
            Write-Host "Rejecting Material PO ID: $PoId" -ForegroundColor Cyan
            $query = "UPDATE Invent_purchase SET postatus='Cancelled', ApprovedBy='$RejectedBy' WHERE poid=$PoId"
        } else {
            # Check if this is a Service PO (invent_subconpurchase table)
            $checkServiceQuery = "SELECT COUNT(*) FROM invent_subconpurchase WHERE poid=$PoId"
            $checkServiceCommand = New-Object System.Data.SqlClient.SqlCommand($checkServiceQuery, $connection)
            $serviceCount = $checkServiceCommand.ExecuteScalar()

            if ($serviceCount -gt 0) {
                # This is a Service PO - update invent_subconpurchase table
                Write-Host "Rejecting Service PO ID: $PoId" -ForegroundColor Cyan
                $query = "UPDATE invent_subconpurchase SET postatus='Cancelled', ApprovedBy='$RejectedBy' WHERE poid=$PoId"
            } else {
                Write-Host "PO ID $PoId not found in either Material or Service PO tables" -ForegroundColor Red
                $connection.Close()
                return $false
            }
        }

        $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
        $result = $command.ExecuteNonQuery()
        $connection.Close()

        Write-Host "PO rejection result: $result rows affected" -ForegroundColor Green
        return $result -gt 0
    }
    catch {
        Write-Host "Error rejecting PO: $($_.Exception.Message)" -ForegroundColor Red
        if ($connection.State -eq 'Open') { $connection.Close() }
        return $false
    }
}

# Create listener
$listener = New-Object System.Net.HttpListener
$listener.Prefixes.Add("http://*:$Port/")

try {
    $listener.Start()
    Write-Host ""
    Write-Host "Server running at: http://***************:$Port" -ForegroundColor Green
    Write-Host ""

    while ($listener.IsListening) {

        $context = $listener.GetContext()
        $request = $context.Request
        $response = $context.Response

        $clientIP = $request.RemoteEndPoint.Address
        $url = $request.Url.AbsolutePath
        $method = $request.HttpMethod

        Write-Host "[$((Get-Date).ToString('HH:mm:ss'))] $clientIP $method $url" -ForegroundColor Yellow

        $response.Headers.Add("Access-Control-Allow-Origin", "*")

        if ($method -eq "OPTIONS") {
            $response.StatusCode = 200
            $response.OutputStream.Close()
            continue
        }

        if ($url -eq "/" -and $method -eq "GET") {
            # Dashboard HTML
            $html = @"
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PO Approval System</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background:
                url('https://images.unsplash.com/photo-1504917595217-d4dc5ebe6122?ixlib=rb-4.0.3') center/cover no-repeat fixed,
                linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(30, 136, 229, 0.3) 30%, rgba(13, 71, 161, 0.4) 60%, rgba(25, 118, 210, 0.3) 100%);
            min-height: 100vh;
            padding: 20px;
            color: #2c3e50;
            position: relative;
            overflow-x: hidden;
        }

        /* Mobile background - use solid industrial colors if image fails */
        @media (max-width: 767px) {
            body {
                background:
                    linear-gradient(135deg, #2c3e50 0%, #34495e 25%, #1e88e5 50%, #2980b9 75%, #3498db 100%),
                    url('https://images.unsplash.com/photo-1504917595217-d4dc5ebe6122?ixlib=rb-4.0.3&w=600&q=80') center/cover no-repeat;
                background-attachment: scroll;
                background-size: cover;
                background-position: center center;
            }

            /* Fallback for very slow connections */
            body::after {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #2c3e50 0%, #34495e 25%, #1e88e5 50%, #2980b9 75%, #3498db 100%);
                z-index: -2;
                opacity: 0.9;
            }
        }

        /* Industrial overlay pattern */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.02) 10px,
                rgba(255,255,255,0.02) 20px
            );
            z-index: -1;
            opacity: 0.4;
            pointer-events: none;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #1e88e5 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow:
                0 15px 35px rgba(0,0,0,0.4),
                inset 0 1px 0 rgba(255,255,255,0.1),
                0 0 0 1px rgba(30, 136, 229, 0.3);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.15);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                0deg,
                transparent,
                transparent 19px,
                rgba(255,255,255,0.03) 19px,
                rgba(255,255,255,0.03) 20px
            ),
            repeating-linear-gradient(
                90deg,
                transparent,
                transparent 19px,
                rgba(255,255,255,0.03) 19px,
                rgba(255,255,255,0.03) 20px
            );
            opacity: 0.3;
            pointer-events: none;
        }

        .header h1, .header p {
            position: relative;
            z-index: 1;
        }
        .login-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 400px;
            margin: 0 auto;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        input:focus {
            border-color: #667eea;
            outline: none;
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .hidden {
            display: none !important;
        }
        .main-content {
            background: rgba(255,255,255,0.98);
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.3);
        }

        /* Mobile main content */
        @media (max-width: 767px) {
            .main-content {
                padding: 15px 12px;
                border-radius: 8px;
                margin: 5px;
            }
        }

        /* Mobile header - make much more compact */
        @media (max-width: 767px) {
            .header {
                margin-bottom: 15px !important;
                padding: 15px 12px !important;
                border-radius: 8px !important;
            }
            .header h1 {
                font-size: 1.2em !important;
                margin: 0 !important;
                line-height: 1.3;
            }
            .header p {
                font-size: 0.9em !important;
                margin: 5px 0 0 0 !important;
            }
            .header img {
                height: 35px !important;
            }
            .header > div {
                gap: 10px !important;
            }
        }

        .user-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* Mobile user info */
        @media (max-width: 767px) {
            .user-info {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
                padding: 20px;
                border-radius: 10px;
                text-align: center;
            }
        }
        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
        }
        .logout-btn:hover {
            background: #c82333;
        }
        .stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        /* Mobile stats */
        @media (max-width: 767px) {
            .stats {
                flex-direction: column;
                gap: 15px;
                text-align: center;
                margin-bottom: 25px;
            }
        }

        .pending-count {
            font-size: 1.5em;
            color: #e74c3c;
            font-weight: bold;
        }

        /* Mobile pending count */
        @media (max-width: 767px) {
            .pending-count {
                font-size: 1.3em;
                padding: 10px;
                background: rgba(231, 76, 60, 0.1);
                border-radius: 8px;
                border-left: 3px solid #e74c3c;
                margin-bottom: 15px;
                width: 100%;
                text-align: center;
                display: block;
            }
        }

        .time-display {
            color: #666;
        }

        /* Mobile time display */
        @media (max-width: 767px) {
            .time-display {
                font-size: 16px;
                padding: 10px;
                background: rgba(102, 102, 102, 0.1);
                border-radius: 8px;
            }
        }
        .refresh-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        .filters {
            margin: 20px 0;
            padding: 25px;
            background: linear-gradient(145deg, rgba(255,255,255,0.98) 0%, rgba(248,249,250,0.95) 100%);
            border-radius: 15px;
            backdrop-filter: blur(15px);
            box-shadow:
                0 8px 32px rgba(0,0,0,0.15),
                inset 0 1px 0 rgba(255,255,255,0.8),
                0 0 0 1px rgba(30, 136, 229, 0.2);
            border: 1px solid rgba(30, 136, 229, 0.3);
            position: relative;
            overflow: hidden;
        }

        .filters::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 25px 25px, rgba(30,136,229,0.06) 1px, transparent 1px),
                        repeating-linear-gradient(0deg, transparent, transparent 24px, rgba(30,136,229,0.04) 24px, rgba(30,136,229,0.04) 25px),
                        repeating-linear-gradient(90deg, transparent, transparent 24px, rgba(30,136,229,0.04) 24px, rgba(30,136,229,0.04) 25px);
            background-size: 50px 50px;
            opacity: 0.4;
            pointer-events: none;
            z-index: 0;
        }

        .filters > * {
            position: relative;
            z-index: 1;
        }

        /* Desktop layout - single line */
        @media (min-width: 768px) {
            .filters {
                display: flex;
                align-items: center;
                gap: 20px;
                flex-wrap: wrap;
            }
            .filter-group {
                display: flex;
                align-items: center;
                gap: 8px;
            }
        }

        /* Mobile layout - line by line */
        @media (max-width: 767px) {
            .filters {
                display: flex;
                flex-direction: column;
                gap: 15px;
            }
            .filter-group {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }
        }

        .filters label {
            font-weight: 600;
            color: #1e88e5;
            font-size: 14px;
            margin: 0;
        }
        .filters select {
            padding: 10px 12px;
            border: 2px solid #e3f2fd;
            border-radius: 8px;
            background: white;
            color: #333;
            font-size: 14px;
            min-width: 200px;
            transition: all 0.3s ease;
        }
        .filters select:focus {
            border-color: #1e88e5;
            outline: none;
            box-shadow: 0 0 0 3px rgba(30, 136, 229, 0.1);
        }
        .po-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.98) 0%, rgba(248,249,250,0.95) 100%);
            margin: 15px 0;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(30, 136, 229, 0.15);
            border-left: 5px solid #1e88e5;
            border: 1px solid rgba(30, 136, 229, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        .po-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(30, 136, 229, 0.25);
            border-left-color: #1565c0;
        }

        /* Mobile-specific PO card styling */
        @media (max-width: 767px) {
            .po-card {
                margin: 20px 10px;
                padding: 25px 20px;
                border-radius: 15px;
            }
        }

        .po-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }

        /* Mobile PO header */
        @media (max-width: 767px) {
            .po-header {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
                margin-bottom: 25px;
            }
        }

        .po-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
            line-height: 1.4;
        }

        /* Mobile PO title */
        @media (max-width: 767px) {
            .po-title {
                font-size: 1.1em;
                margin-bottom: 8px;
            }
        }

        .po-value {
            font-size: 1.1em;
            font-weight: bold;
            color: #27ae60;
            text-align: right;
        }

        /* Mobile PO value */
        @media (max-width: 767px) {
            .po-value {
                font-size: 1.0em;
                text-align: left;
                background: rgba(39, 174, 96, 0.1);
                padding: 8px 12px;
                border-radius: 6px;
                border-left: 3px solid #27ae60;
            }
        }

        .po-details {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .po-details p {
            margin: 8px 0;
            font-size: 14px;
        }

        /* Mobile PO details */
        @media (max-width: 767px) {
            .po-details {
                margin-bottom: 20px;
                line-height: 1.5;
            }
            .po-details p {
                margin: 8px 0;
                font-size: 14px;
                padding: 6px 0;
                border-bottom: 1px solid rgba(30, 136, 229, 0.1);
            }
            .po-details p:last-child {
                border-bottom: none;
            }
        }

        .po-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        /* Mobile PO actions */
        @media (max-width: 767px) {
            .po-actions {
                flex-direction: column;
                gap: 12px;
            }
            .po-actions .view-btn {
                order: 1;
                margin-bottom: 8px;
            }
            .po-actions .approve-reject-row {
                order: 2;
                display: flex;
                gap: 10px;
            }
            .po-actions .approve-reject-row .approve-btn,
            .po-actions .approve-reject-row .reject-btn {
                flex: 1;
            }
        }
        .approve-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        .approve-btn:hover {
            background: #229954;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
        }

        /* Mobile approve button */
        @media (max-width: 767px) {
            .approve-btn {
                padding: 12px 20px;
                font-size: 14px;
                border-radius: 8px;
                width: 100%;
                text-align: center;
            }
        }

        .reject-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        .reject-btn:hover {
            background: #c0392b;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
        }

        /* Mobile reject button */
        @media (max-width: 767px) {
            .reject-btn {
                padding: 12px 20px;
                font-size: 14px;
                border-radius: 8px;
                width: 100%;
                text-align: center;
            }
        }

        .view-btn {
            background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        .view-btn:hover {
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(30, 136, 229, 0.3);
        }

        /* Mobile view button */
        @media (max-width: 767px) {
            .view-btn {
                padding: 12px 20px;
                font-size: 14px;
                border-radius: 8px;
                width: 100%;
                text-align: center;
            }
        }
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            backdrop-filter: blur(15px);
            background: linear-gradient(135deg, rgba(30, 136, 229, 0.15) 0%, rgba(21, 101, 192, 0.25) 100%);
            z-index: 1000;
            animation: modalFadeIn 0.3s ease-out;
            overflow-y: auto;
            padding: 20px 0;
        }

        @keyframes modalFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-content {
            max-width: 95%;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            box-shadow:
                0 30px 90px rgba(0, 0, 0, 0.3),
                0 15px 50px rgba(30, 136, 229, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.9),
                0 0 0 1px rgba(30, 136, 229, 0.2);
            padding: 0;
            margin: 20px;
            border: 1px solid rgba(30, 136, 229, 0.15);
            animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            position: relative;
            flex-shrink: 0;
        }

        .modal-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30px 30px, rgba(30,136,229,0.04) 1px, transparent 1px),
                        repeating-linear-gradient(0deg, transparent, transparent 29px, rgba(30,136,229,0.02) 29px, rgba(30,136,229,0.02) 30px),
                        repeating-linear-gradient(90deg, transparent, transparent 29px, rgba(30,136,229,0.02) 29px, rgba(30,136,229,0.02) 30px);
            background-size: 60px 60px;
            opacity: 0.3;
            pointer-events: none;
            z-index: 0;
        }

        /* Mobile modal content */
        @media (max-width: 767px) {
            .modal-content {
                max-width: 98%;
                max-height: 95%;
                margin: 10px;
                border-radius: 15px;
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 30%, #1e88e5 100%);
            color: white;
            padding: 25px 30px 20px 30px;
            border-radius: 20px 20px 0 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20px 20px, rgba(255,255,255,0.06) 0.8px, transparent 0.8px),
                        repeating-linear-gradient(0deg, transparent, transparent 19px, rgba(255,255,255,0.04) 19px, rgba(255,255,255,0.04) 20px),
                        repeating-linear-gradient(90deg, transparent, transparent 19px, rgba(255,255,255,0.04) 19px, rgba(255,255,255,0.04) 20px);
            background-size: 40px 40px;
            opacity: 0.4;
            pointer-events: none;
            z-index: 0;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5em;
            font-weight: 700;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 1;
        }

        /* Mobile modal header */
        @media (max-width: 767px) {
            .modal-header {
                padding: 15px 20px 12px 20px !important;
                border-radius: 15px 15px 0 0 !important;
            }
            .modal-header h2 {
                font-size: 1.0em !important;
                line-height: 1.2;
                margin-right: 10px;
                word-break: break-word;
            }
            .modal-close-btn {
                padding: 6px 10px !important;
                font-size: 11px !important;
                border-radius: 4px !important;
                min-width: 50px;
            }
        }

        .modal-close-btn {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 16px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 1;
        }

        .modal-close-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .modal-body {
            padding: 35px;
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 0 0 20px 20px;
        }

        /* Mobile modal body */
        @media (max-width: 767px) {
            .modal-body {
                padding: 20px 15px;
                border-radius: 0 0 15px 15px;
                font-size: 14px;
            }
        }

        .form-section {
            background: linear-gradient(145deg, #f8f9fa 0%, #ffffff 100%);
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 1px solid rgba(30, 136, 229, 0.1);
            box-shadow: 0 4px 20px rgba(30, 136, 229, 0.05);
        }

        /* Mobile form section */
        @media (max-width: 767px) {
            .form-section {
                padding: 15px 12px;
                margin-bottom: 15px;
                border-radius: 8px;
            }
        }

        .form-section label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: #1e88e5;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-section input {
            width: 100%;
            padding: 15px 18px;
            border: 2px solid #e3f2fd;
            border-radius: 10px;
            font-size: 16px;
            background: white;
            transition: all 0.3s ease;
            box-shadow: inset 0 2px 4px rgba(30, 136, 229, 0.05);
            box-sizing: border-box;
        }

        /* Mobile form inputs */
        @media (max-width: 767px) {
            .form-section input {
                padding: 12px 15px;
                font-size: 16px;
                border-radius: 8px;
            }
        }

        .form-section input:focus {
            border-color: #1e88e5;
            outline: none;
            box-shadow:
                inset 0 2px 4px rgba(30, 136, 229, 0.05),
                0 0 0 4px rgba(30, 136, 229, 0.1);
            transform: translateY(-1px);
        }

        .modal-actions {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }

        /* Mobile modal actions */
        @media (max-width: 767px) {
            .modal-actions {
                flex-direction: column;
                gap: 15px;
                margin-top: 25px;
            }
        }

        /* Mobile PO info grid - stack vertically */
        @media (max-width: 767px) {
            .po-info-grid {
                grid-template-columns: 1fr !important;
                gap: 20px !important;
            }
        }

        /* Mobile line item details - keep row layout */
        @media (max-width: 767px) {
            .line-item-details {
                padding: 12px !important;
            }
        }

        /* Mobile modal action buttons */
        @media (max-width: 767px) {
            .modal-action-buttons {
                flex-wrap: wrap !important;
                gap: 8px !important;
                padding: 12px !important;
            }
            .modal-action-buttons button {
                padding: 10px 12px !important;
                font-size: 12px !important;
                max-width: none !important;
                min-width: 70px !important;
            }
        }

        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 35px;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 700;
            font-size: 16px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
            border: none;
            padding: 15px 35px;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 700;
            font-size: 16px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.3);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                <img src="/api/logo" alt="PRK Logo" style="height: 50px; width: auto;" onerror="this.style.display='none'">
                <h1 style="margin: 0;">PRK/KKA PO Approval System</h1>
            </div>
            <p>Purchase Order Management</p>
        </div>

        <!-- Login Section -->
        <div id="loginSection" class="login-container">
            <h2>Login Required</h2>
            <div class="form-group">
                <label for="empId">Employee Code:</label>
                <input type="text" id="empId" placeholder="Enter Employee Code (e.g., E0002)">
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" placeholder="Enter Password">
            </div>
            <button onclick="login()" class="btn">Login</button>
            <div id="loginMessage" style="margin-top: 10px; color: red;"></div>
        </div>

        <!-- Main Content (hidden until login) -->
        <div id="mainContent" class="main-content hidden">
            <div class="user-info">
                <span>Logged in as: <strong id="loggedUser"></strong></span>
                <div>
                    <button onclick="showChangePassword()" class="change-password-btn" style="background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%); color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; margin-right: 10px;">Change Password</button>
                    <button onclick="logout()" class="logout-btn">Logout</button>
                </div>
            </div>

            <div class="stats">
                <div>
                    <span>Pending POs: </span>
                    <span id="total-pos" class="pending-count">0</span>
                </div>
                <div>
                    <span id="current-time" class="time-display"></span>
                    <button onclick="refreshData()" class="refresh-btn">Refresh</button>
                </div>
            </div>

            <div class="filters">
                <div class="filter-group">
                    <label>Filter by Supplier:</label>
                    <select id="supplier-filter" onchange="filterPOs()">
                        <option value="">All Suppliers</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>Filter by Location:</label>
                    <select id="location-filter" onchange="filterPOs()">
                        <option value="">All Locations</option>
                    </select>
                </div>
            </div>

            <div id="po-list"></div>
        </div>
    </div>

    <script>
        let currentUser = null;
        let allPOs = [];
        let filteredPOs = [];

        // Login function
        function login() {
            const empId = document.getElementById('empId').value.trim();
            const password = document.getElementById('password').value.trim();
            const messageDiv = document.getElementById('loginMessage');

            messageDiv.textContent = '';

            if (!empId || !password) {
                messageDiv.textContent = 'Please enter both Employee Code and Password';
                return;
            }

            fetch('/api/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ empId: empId, password: password })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentUser = {
                        empCode: data.empCode,
                        empId: data.empId,
                        name: data.name
                    };

                    document.getElementById('loggedUser').textContent = data.name + ' (' + data.empCode + ')';
                    document.getElementById('loginSection').classList.add('hidden');
                    document.getElementById('mainContent').classList.remove('hidden');

                    loadPOData();
                    updateTime();
                    setInterval(updateTime, 1000);
                } else {
                    messageDiv.textContent = 'Login failed: ' + (data.error || 'Invalid credentials');
                }
            })
            .catch(error => {
                messageDiv.textContent = 'Login error: ' + error.message;
            });
        }

        // Logout function
        function logout() {
            currentUser = null;
            document.getElementById('empId').value = '';
            document.getElementById('password').value = '';
            document.getElementById('loginSection').classList.remove('hidden');
            document.getElementById('mainContent').classList.add('hidden');
        }

        // Show change password modal
        function showChangePassword() {
            if (!currentUser) {
                alert('Please login first');
                return;
            }

            // Prevent body scrolling
            document.body.style.overflow = 'hidden';

            const modalHtml = '<div class="modal" id="changePasswordModal">' +
                '<div class="modal-content" style="max-width: 550px;">' +
                '<div class="modal-header" style="display: flex; justify-content: space-between; align-items: center;">' +
                '<h2>Change Password</h2>' +
                '<button onclick="closeChangePasswordModal()" class="modal-close-btn">X Close</button>' +
                '</div>' +
                '<div class="modal-body">' +
                '<div class="form-section">' +
                '<label>Current Password:</label>' +
                '<input type="password" id="currentPassword" placeholder="Enter current password">' +
                '</div>' +
                '<div class="form-section">' +
                '<label>New Password:</label>' +
                '<input type="password" id="newPassword" placeholder="Enter new password">' +
                '</div>' +
                '<div class="form-section">' +
                '<label>Confirm New Password:</label>' +
                '<input type="password" id="confirmPassword" placeholder="Confirm new password">' +
                '</div>' +
                '<div id="passwordMessage" style="margin-bottom: 20px; color: #e74c3c; font-weight: 600; text-align: center; padding: 15px; background: rgba(231, 76, 60, 0.1); border-radius: 8px; border-left: 4px solid #e74c3c; display: none;"></div>' +
                '<div class="modal-actions">' +
                '<button onclick="changePassword()" class="btn-primary">Update Password</button>' +
                '<button onclick="closeChangePasswordModal()" class="btn-secondary">Cancel</button>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';

            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        // Close change password modal
        function closeChangePasswordModal() {
            const modal = document.getElementById('changePasswordModal');
            if (modal) {
                modal.remove();
            }
            // Restore body scrolling
            document.body.style.overflow = 'auto';
        }

        // Change password function
        function changePassword() {
            const currentPassword = document.getElementById('currentPassword').value.trim();
            const newPassword = document.getElementById('newPassword').value.trim();
            const confirmPassword = document.getElementById('confirmPassword').value.trim();
            const messageDiv = document.getElementById('passwordMessage');

            messageDiv.textContent = '';
            messageDiv.style.display = 'none';

            // Validation
            if (!currentPassword || !newPassword || !confirmPassword) {
                messageDiv.textContent = 'Please fill in all fields';
                messageDiv.style.display = 'block';
                return;
            }

            if (newPassword !== confirmPassword) {
                messageDiv.textContent = 'New passwords do not match';
                messageDiv.style.display = 'block';
                return;
            }

            if (newPassword.length < 4) {
                messageDiv.textContent = 'New password must be at least 4 characters long';
                messageDiv.style.display = 'block';
                return;
            }

            // Send change password request
            fetch('/api/change-password', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    empCode: currentUser.empCode,
                    currentPassword: currentPassword,
                    newPassword: newPassword
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    messageDiv.style.color = '#28a745';
                    messageDiv.style.background = 'rgba(40, 167, 69, 0.1)';
                    messageDiv.style.borderLeftColor = '#28a745';
                    messageDiv.textContent = 'Password changed successfully!';
                    messageDiv.style.display = 'block';
                    setTimeout(() => {
                        closeChangePasswordModal();
                    }, 2000);
                } else {
                    messageDiv.style.color = '#e74c3c';
                    messageDiv.style.background = 'rgba(231, 76, 60, 0.1)';
                    messageDiv.style.borderLeftColor = '#e74c3c';
                    messageDiv.textContent = 'Error: ' + (data.error || 'Failed to change password');
                    messageDiv.style.display = 'block';
                }
            })
            .catch(error => {
                messageDiv.style.color = '#e74c3c';
                messageDiv.style.background = 'rgba(231, 76, 60, 0.1)';
                messageDiv.style.borderLeftColor = '#e74c3c';
                messageDiv.textContent = 'Error: ' + error.message;
                messageDiv.style.display = 'block';
            });
        }

        // Enter key support
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('empId').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') login();
            });
            document.getElementById('password').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') login();
            });
        });

        // Load PO data
        function loadPOData() {
            fetch('/api/pos')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        allPOs = data.data;
                        filteredPOs = [...allPOs];
                        updatePODisplay();
                        populateFilters();
                    }
                })
                .catch(error => console.error('Error loading PO data:', error));
        }

        // Update time display
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString();
        }

        // Refresh data
        function refreshData() {
            loadPOData();
        }

        // Update PO display
        function updatePODisplay() {
            const totalElement = document.getElementById('total-pos');
            const listElement = document.getElementById('po-list');

            totalElement.textContent = filteredPOs.length;

            // PO list
            let listHtml = '';
            filteredPOs.forEach(po => {
                const totalValue = po.TotalValue || 0;
                const displayValue = totalValue === 0 ? 'Rate Contract' : 'Rs. ' + totalValue.toLocaleString();

                listHtml += '<div class="po-card">';
                listHtml += '<div class="po-header">';
                listHtml += '<div class="po-title">PO: ' + (po.PoNo || 'N/A') + '</div>';
                listHtml += '<div class="po-value">' + displayValue + '</div>';
                listHtml += '</div>';
                listHtml += '<div class="po-details">';
                listHtml += '<p><strong>Supplier:</strong> ' + (po.Supplier || 'N/A') + '</p>';
                listHtml += '<p><strong>Location:</strong> ' + (po.Location || 'N/A') + '</p>';
                listHtml += '<p><strong>Items:</strong> ' + (po.ItemCount || 0) + '</p>';
                listHtml += '</div>';
                listHtml += '<div class="po-actions">';
                listHtml += '<button onclick="viewPO(' + po.PoId + ')" class="view-btn">View Details</button>';
                listHtml += '<div class="approve-reject-row">';
                listHtml += '<button onclick="approvePO(' + po.PoId + ')" class="approve-btn">Approve</button>';
                listHtml += '<button onclick="rejectPO(' + po.PoId + ')" class="reject-btn">Reject</button>';
                listHtml += '</div>';
                listHtml += '</div>';
                listHtml += '</div>';
            });

            listElement.innerHTML = listHtml;
        }

        // Populate filter dropdowns
        function populateFilters() {
            const supplierFilter = document.getElementById('supplier-filter');
            const locationFilter = document.getElementById('location-filter');

            // Clear existing options (except "All")
            supplierFilter.innerHTML = '<option value="">All Suppliers</option>';
            locationFilter.innerHTML = '<option value="">All Locations</option>';

            // Get unique suppliers and locations
            const suppliers = [...new Set(allPOs.map(po => po.Supplier).filter(s => s))];
            const locations = [...new Set(allPOs.map(po => po.Location).filter(l => l))];

            suppliers.forEach(supplier => {
                const option = document.createElement('option');
                option.value = supplier;
                option.textContent = supplier;
                supplierFilter.appendChild(option);
            });

            locations.forEach(location => {
                const option = document.createElement('option');
                option.value = location;
                option.textContent = location;
                locationFilter.appendChild(option);
            });
        }

        // Filter POs
        function filterPOs() {
            const selectedSupplier = document.getElementById('supplier-filter').value;
            const selectedLocation = document.getElementById('location-filter').value;

            filteredPOs = allPOs.filter(po => {
                const supplierMatch = !selectedSupplier || po.Supplier === selectedSupplier;
                const locationMatch = !selectedLocation || po.Location === selectedLocation;
                return supplierMatch && locationMatch;
            });

            updatePODisplay();
        }

        // View PO details
        function viewPO(poId) {
            const po = allPOs.find(p => p.PoId === poId);
            if (!po) {
                alert('PO not found!');
                return;
            }

            // Prevent body scrolling
            document.body.style.overflow = 'hidden';

            // Create detailed modal with proper string concatenation
            let modalHtml = '<div class="modal" id="poDetailModal">';
            modalHtml += '<div class="modal-content">';
            modalHtml += '<div class="modal-header" style="display: flex; justify-content: space-between; align-items: center;">';
            modalHtml += '<h2 class="modal-title">PO: ' + po.PoNo + '</h2>';
            modalHtml += '<button onclick="closeDetailModal()" class="modal-close-btn">Close</button>';
            modalHtml += '</div>';
            modalHtml += '<div class="modal-body">';

            // PO Information sections - Mobile responsive
            modalHtml += '<div class="po-info-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 25px; margin-bottom: 35px;">';
            modalHtml += '<div style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 3px solid #495057;">';
            modalHtml += '<div style="font-size: 0.95em; font-weight: 600; color: #495057; margin-bottom: 12px;">Purchase Order Information</div>';
            modalHtml += '<div style="line-height: 1.5; color: #6c757d; font-size: 0.9em;">';
            modalHtml += '<div style="margin-bottom: 8px; word-break: break-word;"><strong style="color: #495057;">PO Number:</strong><br>' + po.PoNo + '</div>';
            modalHtml += '<div style="margin-bottom: 8px;"><strong style="color: #495057;">PO ID:</strong><br>' + po.PoId + '</div>';
            modalHtml += '<div style="margin-bottom: 8px; word-break: break-word;"><strong style="color: #495057;">Location:</strong><br>' + po.Location + '</div>';
            modalHtml += '<div style="word-break: break-word;"><strong style="color: #495057;">Created by:</strong><br>' + po.CreatedBy + '</div>';
            modalHtml += '</div></div>';

            modalHtml += '<div style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 3px solid #6c757d;">';
            modalHtml += '<div style="font-size: 0.95em; font-weight: 600; color: #6c757d; margin-bottom: 12px;">Supplier Details</div>';
            modalHtml += '<div style="line-height: 1.5; color: #6c757d; font-size: 0.9em;">';
            modalHtml += '<div style="margin-bottom: 8px; word-break: break-word;"><strong style="color: #495057;">Supplier:</strong><br>' + po.Supplier + '</div>';
            modalHtml += '<div style="margin-bottom: 8px;"><strong style="color: #495057;">Type:</strong><br>' + po.POType + '</div>';
            modalHtml += '<div style="margin-bottom: 8px;"><strong style="color: #495057;">Total Items:</strong><br>' + po.ItemCount + '</div>';
            modalHtml += '<div><strong style="color: #495057;">Status:</strong><br><span style="background: #ffc107; color: #212529; padding: 3px 8px; border-radius: 3px; font-size: 0.8em; font-weight: 600;">Pending Approval</span></div>';
            modalHtml += '</div></div></div>';

            // Line Items
            modalHtml += '<div style="margin-bottom: 35px;">';
            modalHtml += '<div style="font-size: 1.0em; font-weight: 600; color: #495057; margin-bottom: 20px; padding: 12px; background: #f8f9fa; border-radius: 6px; border-left: 3px solid #6c757d;">Detailed Line Items (' + po.ItemCount + ' items)</div>';

            if (po.LineItems && po.LineItems.length > 0) {
                po.LineItems.forEach(function(item, index) {
                    modalHtml += '<div style="background: #ffffff; border: 1px solid #dee2e6; border-radius: 8px; margin-bottom: 20px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">';
                    modalHtml += '<div style="background: #495057; color: white; padding: 12px 15px;">';
                    modalHtml += '<div style="font-size: 0.95em; font-weight: 600; margin-bottom: 8px; word-break: break-word;">' + item.Material + '</div>';
                    modalHtml += '<div style="font-size: 1.0em; font-weight: 700; background: rgba(255,255,255,0.15); padding: 4px 8px; border-radius: 3px; display: inline-block;">Rs.' + Math.round(item.Amount).toLocaleString('en-IN') + '</div>';
                    modalHtml += '</div>';
                    modalHtml += '<div class="line-item-details" style="padding: 15px;">';
                    // First row: Grade + Quantity
                    modalHtml += '<div style="display: flex; gap: 10px; margin-bottom: 10px;">';
                    modalHtml += '<div style="background: #f8f9fa; padding: 10px; border-radius: 4px; border-left: 2px solid #6c757d; flex: 1;">';
                    modalHtml += '<div style="font-size: 0.7em; color: #6c757d; margin-bottom: 3px; text-transform: uppercase; letter-spacing: 0.3px;">MATERIAL GRADE</div>';
                    modalHtml += '<div style="font-weight: 600; color: #495057; font-size: 0.85em;">' + (item.Grade || 'N/A') + '</div>';
                    modalHtml += '</div>';
                    modalHtml += '<div style="background: #f8f9fa; padding: 10px; border-radius: 4px; border-left: 2px solid #6c757d; flex: 1;">';
                    modalHtml += '<div style="font-size: 0.7em; color: #6c757d; margin-bottom: 3px; text-transform: uppercase; letter-spacing: 0.3px;">QUANTITY</div>';
                    modalHtml += '<div style="font-weight: 600; color: #495057; font-size: 0.85em;">' + parseFloat(item.Quantity).toLocaleString('en-IN') + ' ' + item.UOM + '</div>';
                    modalHtml += '</div>';
                    modalHtml += '</div>';
                    // Second row: Unit Rate + HSN Code
                    modalHtml += '<div style="display: flex; gap: 10px;">';
                    modalHtml += '<div style="background: #f8f9fa; padding: 10px; border-radius: 4px; border-left: 2px solid #6c757d; flex: 1;">';
                    modalHtml += '<div style="font-size: 0.7em; color: #6c757d; margin-bottom: 3px; text-transform: uppercase; letter-spacing: 0.3px;">UNIT RATE</div>';
                    modalHtml += '<div style="font-weight: 600; color: #495057; font-size: 0.85em;">Rs.' + parseFloat(item.Rate).toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) + '</div>';
                    modalHtml += '</div>';
                    modalHtml += '<div style="background: #f8f9fa; padding: 10px; border-radius: 4px; border-left: 2px solid #6c757d; flex: 1;">';
                    modalHtml += '<div style="font-size: 0.7em; color: #6c757d; margin-bottom: 3px; text-transform: uppercase; letter-spacing: 0.3px;">HSN CODE</div>';
                    modalHtml += '<div style="font-weight: 600; color: #495057; font-size: 0.85em; font-family: monospace;">' + (item.HSNCode || 'N/A') + '</div>';
                    modalHtml += '</div>';
                    modalHtml += '</div>';
                    modalHtml += '</div></div>';
                });
            }
            modalHtml += '</div>';

            // Terms and Conditions
            if (po.Terms) {
                modalHtml += '<div style="margin-bottom: 35px;">';
                modalHtml += '<div style="font-size: 1.2em; font-weight: 600; color: #495057; margin-bottom: 15px; padding: 15px; background: #e3f2fd; border-radius: 8px; border-left: 4px solid #1976d2;">Terms and Conditions</div>';
                modalHtml += '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6; white-space: pre-line; line-height: 1.6; color: #495057;">' + po.Terms + '</div>';
                modalHtml += '</div>';
            }

            // Action buttons
            modalHtml += '<div class="modal-action-buttons" style="display: flex; justify-content: center; gap: 10px; padding: 15px; background: #f8f9fa; border-radius: 6px; margin-top: 20px;">';
            modalHtml += '<button onclick="approvePOFromModal(' + po.PoId + ')" style="background: linear-gradient(135deg, #28a745 0%, #218838 100%); color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-weight: 600; font-size: 13px; flex: 1; max-width: 100px;">APPROVE</button>';
            modalHtml += '<button onclick="rejectPOFromModal(' + po.PoId + ')" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-weight: 600; font-size: 13px; flex: 1; max-width: 100px;">REJECT</button>';
            modalHtml += '<button onclick="closeDetailModal()" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-weight: 600; font-size: 13px; flex: 1; max-width: 80px;">CLOSE</button>';
            modalHtml += '</div>';
            modalHtml += '</div>'; // Close padding div
            modalHtml += '</div>'; // Close modal-content
            modalHtml += '</div>'; // Close modal

            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        function closeDetailModal() {
            const modal = document.getElementById('poDetailModal');
            if (modal) {
                modal.remove();
            }
            // Restore body scrolling
            document.body.style.overflow = 'auto';
        }

        function approvePOFromModal(poId) {
            closeDetailModal();
            approvePO(poId);
        }

        function rejectPOFromModal(poId) {
            closeDetailModal();
            rejectPO(poId);
        }

        // Approve PO
        function approvePO(poId) {
            if (!currentUser) {
                alert('Please login first');
                return;
            }

            if (!confirm('Are you sure you want to approve this PO?')) {
                return;
            }

            fetch('/api/approve', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    poId: poId,
                    approvedBy: currentUser.empId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('PO approved successfully!');
                    loadPOData(); // Refresh the list
                } else {
                    alert('Error approving PO: ' + data.message);
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }

        // Reject PO
        function rejectPO(poId) {
            if (!currentUser) {
                alert('Please login first');
                return;
            }

            if (!confirm('Are you sure you want to reject this PO?')) {
                return;
            }

            fetch('/api/reject', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    poId: poId,
                    rejectedBy: currentUser.empId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('PO rejected successfully!');
                    loadPOData(); // Refresh the list
                } else {
                    alert('Error rejecting PO: ' + data.message);
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }
    </script>
</body>
</html>
"@

            # Add cache-busting headers
            $response.Headers.Add("Cache-Control", "no-cache, no-store, must-revalidate")
            $response.Headers.Add("Pragma", "no-cache")
            $response.Headers.Add("Expires", "0")

            $buffer = [System.Text.Encoding]::UTF8.GetBytes($html)
            $response.ContentLength64 = $buffer.Length
            $response.ContentType = "text/html"
            $response.OutputStream.Write($buffer, 0, $buffer.Length)
            $response.OutputStream.Close()
        }
        elseif ($url -eq "/api/login" -and $method -eq "POST") {
            # Login endpoint
            $reader = New-Object System.IO.StreamReader($request.InputStream)
            $body = $reader.ReadToEnd()
            $reader.Close()

            try {
                $data = $body | ConvertFrom-Json
                $loginName = $data.empId
                $password = $data.password

                if ($loginName -and $password) {
                    $connection = Get-DatabaseConnection

                    # Check if user has custom password in API_Employee table
                    $customPasswordQuery = "SELECT Password FROM API_Employee WHERE EmpCode='$loginName'"
                    $customCommand = New-Object System.Data.SqlClient.SqlCommand($customPasswordQuery, $connection)

                    try {
                        $customReader = $customCommand.ExecuteReader()
                        $customPassword = $null
                        if ($customReader.Read()) {
                            $customPassword = $customReader["Password"]
                        }
                        $customReader.Close()
                    } catch {
                        # API_Employee table might not exist, that's OK
                        $customPassword = $null
                    }

                    # Check password (custom or default 1234)
                    $expectedPassword = if ($customPassword) { $customPassword } else { "1234" }

                    if ($password -eq $expectedPassword) {
                        # Get employee details using EMPCODE
                        $query = "SELECT EmpName, EmpCode, EmpId FROM Employee WHERE DeptId IN (20061,20062,20071,20072,20048,20049,20087,20088,20063) AND EmpName <> 'SOMALA' AND EmpCode='$loginName'"
                        $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
                        $reader = $command.ExecuteReader()

                        if ($reader.Read()) {
                            $empName = $reader["EmpName"]
                            $empCode = $reader["EmpCode"]
                            $empId = $reader["EmpId"]
                            $reader.Close()

                            # Login successful

                            # Update last login date in API_Employee table
                            try {
                                $updateLoginQuery = @"
                                    IF EXISTS (SELECT 1 FROM API_Employee WHERE EmpCode='$loginName')
                                        UPDATE API_Employee SET LastLoginDate=GETDATE() WHERE EmpCode='$loginName'
                                    ELSE
                                        INSERT INTO API_Employee (EmpCode, EmpName, EmpId, Password, CreatedBy, ModifiedBy)
                                        VALUES ('$loginName', '$empName', $empId, '1234', 'SYSTEM', 'SYSTEM')
"@
                                $updateLoginCommand = New-Object System.Data.SqlClient.SqlCommand($updateLoginQuery, $connection)
                                $updateLoginCommand.ExecuteNonQuery()
                            } catch {
                                # Ignore if table doesn't exist yet
                            }

                            $connection.Close()

                            Send-JSON -Response $response -Data @{
                                success = $true
                                name = $empName
                                empCode = $empCode
                                empId = $empId
                                loginName = $loginName
                                hasCustomPassword = ($customPassword -ne $null)
                            }
                        } else {
                            $reader.Close()
                            $connection.Close()
                            # Login failed - employee not found
                            Send-JSON -Response $response -Data @{
                                success = $false
                                error = "Employee code not found or not authorized"
                            }
                        }
                    } else {
                        $connection.Close()
                        # Login failed - invalid password
                        Send-JSON -Response $response -Data @{
                            success = $false
                            error = "Invalid password"
                        }
                    }
                } else {
                    Send-JSON -Response $response -Data @{
                        success = $false
                        error = "Please enter both login name and password"
                    }
                }
            } catch {
                Send-JSON -Response $response -Data @{
                    success = $false
                    error = "Login error: $($_.Exception.Message)"
                }
            }
        }
        elseif ($url -eq "/api/pos" -and $method -eq "GET") {
            # Get grouped POs
            $groupedPOs = Get-GroupedPOs

            # If no results, try the simplified query
            if ($groupedPOs.Count -eq 0) {
                Write-Host "No results from standard query, trying simplified query..." -ForegroundColor Yellow
                $groupedPOs = Get-SimplifiedPendingPOs
            }

            Send-JSON -Response $response -Data @{
                success = $true
                count = $groupedPOs.Count
                data = $groupedPOs
            }
        }
        elseif ($url -eq "/api/approve" -and $method -eq "POST") {
            # Approve PO
            $reader = New-Object System.IO.StreamReader($request.InputStream)
            $body = $reader.ReadToEnd()
            $reader.Close()

            $data = $body | ConvertFrom-Json
            $success = Approve-PO -PoId $data.poId -ApprovedBy $data.approvedBy

            # PO approval processed

            Send-JSON -Response $response -Data @{
                success = $success
                message = if ($success) { "PO approved" } else { "Approval failed" }
            }
        }
        elseif ($url -eq "/api/reject" -and $method -eq "POST") {
            # Reject PO
            $reader = New-Object System.IO.StreamReader($request.InputStream)
            $body = $reader.ReadToEnd()
            $reader.Close()

            $data = $body | ConvertFrom-Json
            $success = Reject-PO -PoId $data.poId -RejectedBy $data.rejectedBy

            # PO rejection processed

            Send-JSON -Response $response -Data @{
                success = $success
                message = if ($success) { "PO rejected" } else { "Rejection failed" }
            }
        }
        elseif ($url -eq "/api/logo" -and $method -eq "GET") {
            # Serve company logo
            $logoPath = "C:\IcSoft Server Setup Ver 4.0 D\Attachments\PRK Logo.JPG"

            if (Test-Path $logoPath) {
                try {
                    $logoBytes = [System.IO.File]::ReadAllBytes($logoPath)
                    $response.ContentType = "image/jpeg"
                    $response.ContentLength64 = $logoBytes.Length
                    $response.OutputStream.Write($logoBytes, 0, $logoBytes.Length)
                    $response.OutputStream.Close()
                } catch {
                    # If error reading file, return 404
                    $response.StatusCode = 404
                    $response.OutputStream.Close()
                }
            } else {
                # File not found
                $response.StatusCode = 404
                $response.OutputStream.Close()
            }
        }
        elseif ($url -eq "/api/change-password" -and $method -eq "POST") {
            # Change password endpoint
            $reader = New-Object System.IO.StreamReader($request.InputStream)
            $body = $reader.ReadToEnd()
            $reader.Close()

            try {
                $data = $body | ConvertFrom-Json
                $empCode = $data.empCode
                $currentPassword = $data.currentPassword
                $newPassword = $data.newPassword

                if ($empCode -and $currentPassword -and $newPassword) {
                    $connection = Get-DatabaseConnection

                    # First verify current password
                    $customPasswordQuery = "SELECT Password FROM API_Employee WHERE EmpCode='$empCode'"
                    $customCommand = New-Object System.Data.SqlClient.SqlCommand($customPasswordQuery, $connection)

                    try {
                        $customReader = $customCommand.ExecuteReader()
                        $storedPassword = $null
                        if ($customReader.Read()) {
                            $storedPassword = $customReader["Password"]
                        }
                        $customReader.Close()
                    } catch {
                        $storedPassword = $null
                    }

                    # Check current password (custom or default 1234)
                    $expectedPassword = if ($storedPassword) { $storedPassword } else { "1234" }

                    if ($currentPassword -eq $expectedPassword) {
                        # Update password in API_Employee table
                        try {
                            $updatePasswordQuery = @"
                                IF EXISTS (SELECT 1 FROM API_Employee WHERE EmpCode='$empCode')
                                    UPDATE API_Employee SET Password='$newPassword', ModifiedDate=GETDATE(), ModifiedBy='$empCode' WHERE EmpCode='$empCode'
                                ELSE
                                BEGIN
                                    -- Get employee details for new record
                                    DECLARE @EmpName NVARCHAR(100), @EmpId INT
                                    SELECT @EmpName = EmpName, @EmpId = EmpId FROM Employee WHERE EmpCode='$empCode'

                                    INSERT INTO API_Employee (EmpCode, EmpName, EmpId, Password, CreatedBy, ModifiedBy)
                                    VALUES ('$empCode', @EmpName, @EmpId, '$newPassword', '$empCode', '$empCode')
                                END
"@
                            $updatePasswordCommand = New-Object System.Data.SqlClient.SqlCommand($updatePasswordQuery, $connection)
                            $updatePasswordCommand.ExecuteNonQuery()
                            $connection.Close()

                            # Password changed successfully

                            Send-JSON -Response $response -Data @{
                                success = $true
                                message = "Password changed successfully"
                            }
                        } catch {
                            $connection.Close()
                            Send-JSON -Response $response -Data @{
                                success = $false
                                error = "Failed to update password: $($_.Exception.Message)"
                            }
                        }
                    } else {
                        $connection.Close()
                        Send-JSON -Response $response -Data @{
                            success = $false
                            error = "Current password is incorrect"
                        }
                    }
                } else {
                    Send-JSON -Response $response -Data @{
                        success = $false
                        error = "Please provide all required fields"
                    }
                }
            } catch {
                Send-JSON -Response $response -Data @{
                    success = $false
                    error = "Password change error: $($_.Exception.Message)"
                }
            }
        }
        else {
            $response.StatusCode = 404
            $response.OutputStream.Close()
        }
    }
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
finally {
    if ($listener.IsListening) {
        $listener.Stop()
        Write-Host "Server stopped" -ForegroundColor Yellow
    }
}

# Simplified query function to get basic PO data without complex approval logic
function Get-SimplifiedPendingPOs {
    try {
        $connection = Get-DatabaseConnection
        if (-not $connection) { return @() }

        Write-Host "Using simplified PO query (bypassing complex approval logic)..." -ForegroundColor Yellow

        # Simplified query for Material POs
        $materialQuery = @"
        SELECT TOP 20
            d.PoId,
            d.PoNo,
            d.PoDate,
            d.Addnlparameter AS POType,
            d.SupName AS Supplier,
            d.TotalPoValue AS TotalValue,
            d.EMPNAME AS CreatedBy,
            'Unknown' AS Location,
            'Material' AS POCategory
        FROM Invent_PoDetails_Query d
        WHERE d.PoStatus = 'Approval Pending'

        UNION ALL

        SELECT TOP 20
            iscp.poid AS PoId,
            iscp.Pono AS PoNo,
            iscp.Podate AS PoDate,
            iscp.addnlparameter AS POType,
            sc.supname AS Supplier,
            ISNULL(iscp.TotalSubconPurchaseValue, 0) AS TotalValue,
            e.empname AS CreatedBy,
            com.Location AS Location,
            'Service' AS POCategory
        FROM invent_subconpurchase iscp
        INNER JOIN invent_supplier sc ON sc.supid = iscp.subconid
        INNER JOIN Employee e ON e.EmpId = iscp.entryempid
        INNER JOIN company com ON com.CompanyID = iscp.locationid
        WHERE iscp.PoStatus = 'Approval Pending'

        ORDER BY PoDate DESC
"@

        $command = New-Object System.Data.SqlClient.SqlCommand($materialQuery, $connection)
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
        $dataTable = New-Object System.Data.DataTable
        $adapter.Fill($dataTable)
        $connection.Close()

        Write-Host "Simplified query returned $($dataTable.Rows.Count) rows" -ForegroundColor Green

        $results = @()
        foreach ($row in $dataTable.Rows) {
            $totalValue = 0
            try {
                $totalValue = [double]$row["TotalValue"]
            } catch {
                $totalValue = 0
            }

            $item = @{
                PoId = $row["PoId"]
                PoNo = $row["PoNo"]
                PoDate = $row["PoDate"]
                Location = $row["Location"]
                POType = $row["POType"]
                Supplier = $row["Supplier"]
                TotalValue = $totalValue
                CreatedBy = $row["CreatedBy"]
                POCategory = $row["POCategory"]
                LineItems = @()
                ItemCount = 1
                Terms = ""
                QuotTerms = ""
            }
            $results += $item
        }

        return $results
    }
    catch {
        Write-Host "Error in simplified query: $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

