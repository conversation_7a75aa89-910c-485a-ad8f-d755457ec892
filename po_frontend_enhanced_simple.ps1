# Enhanced PO Approval Frontend - Simple Version
param([int]$Port = 8090)

# Import database service with full path
. "C:\Users\<USER>\Documents\augment-projects\PO APPROVAL\database_service.ps1"

Write-Host "Starting Enhanced PO Approval Frontend" -ForegroundColor Green
Write-Host "Port: $Port" -ForegroundColor Cyan

# Test database
$dbTest = Test-DatabaseConnection
if (-not $dbTest) {
    Write-Host "Database connection failed!" -ForegroundColor Red
    exit 1
}

# Configure firewall
netsh advfirewall firewall delete rule name="PO Frontend Enhanced Simple" 2>$null
netsh advfirewall firewall add rule name="PO Frontend Enhanced Simple" dir=in action=allow protocol=TCP localport=$Port

# Function to validate user credentials
function Test-UserCredentials {
    param($Username, $Password)
    try {
        Write-Host "Attempting login for user: $Username" -ForegroundColor Cyan
        $connection = Get-DatabaseConnection
        $query = @"
            SELECT EmpCode, EmpName, EmpId 
            FROM API_Employee 
            WHERE EmpCode = @Username 
            AND Password = @Password 
            AND IsActive = 1
"@
        Write-Host "Executing login query..." -ForegroundColor Cyan
        $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
        $command.Parameters.AddWithValue("@Username", $Username) | Out-Null
        $command.Parameters.AddWithValue("@Password", $Password) | Out-Null
        
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
        $dataTable = New-Object System.Data.DataTable
        $adapter.Fill($dataTable)
        $connection.Close()

        Write-Host "Query executed. Found $($dataTable.Rows.Count) matching rows." -ForegroundColor Cyan

        if ($dataTable.Rows.Count -eq 1) {
            Write-Host "Login successful for user: $Username" -ForegroundColor Green
            return @{
                Success = $true
                EmpCode = $dataTable.Rows[0]["EmpCode"]
                EmpName = $dataTable.Rows[0]["EmpName"]
                EmpId = $dataTable.Rows[0]["EmpId"]
            }
        }
        Write-Host "Login failed: Invalid credentials for user: $Username" -ForegroundColor Yellow
        return @{ Success = $false }
    }
    catch {
        Write-Host "Error validating credentials: $($_.Exception.Message)" -ForegroundColor Red
        return @{ Success = $false }
    }
}

# JSON response function
function Send-JSON {
    param($Response, $Data)
    $Response.ContentType = "application/json"
    $Response.Headers.Add("Access-Control-Allow-Origin", "*")
    $json = $Data | ConvertTo-Json -Depth 10
    $buffer = [System.Text.Encoding]::UTF8.GetBytes($json)
    $Response.ContentLength64 = $buffer.Length
    $Response.OutputStream.Write($buffer, 0, $buffer.Length)
    $Response.OutputStream.Close()
}

# Create HTML template
$htmlTemplate = @"
<!DOCTYPE html>
<html>
<head>
    <title>PO Approval System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        tr:hover { background-color: #f5f5f5; }
        .filter-section { margin-bottom: 20px; }
        .btn { padding: 5px 10px; margin: 2px; cursor: pointer; }
        .btn-primary { background-color: #4CAF50; color: white; border: none; }
        .btn-secondary { background-color: #008CBA; color: white; border: none; }
    </style>
</head>
<body>
    <div id="content">
        <h2>PO Approval System</h2>
        <div class="filter-section">
            <label>Purchase Type:</label>
            <select id="purchaseTypeFilter">
                <option value="">All Types</option>
            </select>
            <button onclick="refreshData()" class="btn btn-secondary">Refresh</button>
        </div>
        <table>
            <thead>
                <tr>
                    <th>PO No</th>
                    <th>Date</th>
                    <th>Type</th>
                    <th>Supplier</th>
                    <th>Material</th>
                    <th>Quantity</th>
                    <th>Value</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="poTable">
            </tbody>
        </table>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:$Port';

        async function fetchPurchaseTypes() {
            try {
                const response = await fetch(`\${API_BASE_URL}/api/purchase-types`);
                const types = await response.json();
                const select = document.getElementById('purchaseTypeFilter');
                types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type;
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('Error:', error);
            }
        }

        async function fetchPendingPOs(purchaseType = '') {
            try {
                const response = await fetch(`\${API_BASE_URL}/api/pending-pos?type=\${encodeURIComponent(purchaseType)}`);
                const data = await response.json();
                const tbody = document.getElementById('poTable');
                tbody.innerHTML = '';
                
                data.forEach(po => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>\${po.PoNo}</td>
                        <td>\${new Date(po.PoDate).toLocaleDateString()}</td>
                        <td>\${po.POType}</td>
                        <td>\${po.Supplier}</td>
                        <td>\${po.Material}</td>
                        <td>\${po.Quantity} \${po.UOM}</td>
                        <td>₹\${po.POValue.toFixed(2)}</td>
                        <td>
                            <button onclick="viewPO('\${po.PoId}')" class="btn btn-primary">View</button>
                        </td>
                    `;
                    tbody.appendChild(tr);
                });
            } catch (error) {
                console.error('Error:', error);
            }
        }

        function refreshData() {
            const purchaseType = document.getElementById('purchaseTypeFilter').value;
            fetchPendingPOs(purchaseType);
        }

        document.getElementById('purchaseTypeFilter').addEventListener('change', refreshData);

        // Initialize
        fetchPurchaseTypes();
        fetchPendingPOs();
    </script>
</body>
</html>
"@

# Create the HTTP listener
$listener = New-Object System.Net.HttpListener
$listener.Prefixes.Add("http://localhost:$Port/")
$listener.Start()

Write-Host "Server is running on http://localhost:$Port/" -ForegroundColor Green

while ($listener.IsListening) {
    $context = $listener.GetContext()
    $request = $context.Request
    $response = $context.Response

    $path = $request.Url.LocalPath
    Write-Host "[$($request.HttpMethod)] $path" -ForegroundColor Cyan

    # Handle CORS preflight
    if ($request.HttpMethod -eq 'OPTIONS') {
        $response.Headers.Add("Access-Control-Allow-Origin", "*")
        $response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
        $response.Headers.Add("Access-Control-Allow-Headers", "Content-Type")
        $response.StatusCode = 200
        $response.Close()
        continue
    }

    switch -regex ($path) {
        '^/$' {
            # Check for URL parameters first
            $username = $request.QueryString["username"]
            $password = $request.QueryString["password"]
            
            if ($username -and $password) {
                Write-Host "Attempting login with URL parameters for user: $username" -ForegroundColor Cyan
                $authResult = Test-UserCredentials -Username $username -Password $password
                if ($authResult.Success) {
                    $response.ContentType = "text/html"
                    $buffer = [System.Text.Encoding]::UTF8.GetBytes($htmlTemplate)
                    $response.ContentLength64 = $buffer.Length
                    $response.OutputStream.Write($buffer, 0, $buffer.Length)
                    $response.OutputStream.Close()
                } else {
                    $response.StatusCode = 401
                    $response.ContentType = "text/plain"
                    $message = "Invalid credentials"
                    $buffer = [System.Text.Encoding]::UTF8.GetBytes($message)
                    $response.ContentLength64 = $buffer.Length
                    $response.OutputStream.Write($buffer, 0, $buffer.Length)
                    $response.OutputStream.Close()
                }
            } else {
                $response.StatusCode = 401
                $response.ContentType = "text/plain"
                $message = "Login required. Use: http://localhost:$Port/?username=YOUR_USERNAME&password=YOUR_PASSWORD"
                $buffer = [System.Text.Encoding]::UTF8.GetBytes($message)
                $response.ContentLength64 = $buffer.Length
                $response.OutputStream.Write($buffer, 0, $buffer.Length)
                $response.OutputStream.Close()
            }
        }
        
        '^/api/purchase-types$' {
            $types = Get-PurchaseTypes
            Send-JSON -Response $response -Data $types
        }
        
        '^/api/pending-pos$' {
            $purchaseType = $request.QueryString["type"]
            if ([string]::IsNullOrEmpty($purchaseType)) {
                $purchaseType = '%'
            }
            $pos = Get-PendingPOs -PurchaseType $purchaseType
            Send-JSON -Response $response -Data $pos
        }
        
        default {
            $response.StatusCode = 404
            $response.Close()
        }
    }
} 