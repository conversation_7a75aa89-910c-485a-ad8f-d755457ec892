# Script to check passwords in API_Employee table
. "C:\Users\<USER>\Documents\augment-projects\PO APPROVAL\database_service.ps1"

Write-Host "Checking passwords in API_Employee table..." -ForegroundColor Cyan

$connection = Get-DatabaseConnection

# Check passwords
$query = "SELECT EmpCode, EmpName, Password, IsActive FROM API_Employee"
$command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
$reader = $command.ExecuteReader()

Write-Host "`nUser credentials in API_Employee table:" -ForegroundColor Green
while ($reader.Read()) {
    Write-Host "EmpCode: $($reader['EmpCode']), Name: $($reader['EmpName']), Password: $($reader['Password']), Active: $($reader['IsActive'])" -ForegroundColor Yellow
}

$reader.Close()
$connection.Close() 