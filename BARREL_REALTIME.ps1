# Configuration
$server = "WIN-PRK-SRV-01"
$mainDbConn = "Server=$server;Database=IcSoft;Integrated Security=True;Encrypt=False"

# Email Configuration
$smtpServer = "smtp.gmail.com"
$smtpPort = 587
$from = "<EMAIL>"
$to = @("<EMAIL>")
$subject = "Barrel Real Time Stock Report - $(Get-Date -Format 'dd MMM yyyy')"
$smtpUser = "<EMAIL>"
$smtpPass = ConvertTo-SecureString "mbyieveahdyrdgfc" -AsPlainText -Force
$creds = New-Object PSCredential($smtpUser, $smtpPass)

# Execute SQL Query
try {
    $connection = New-Object System.Data.SqlClient.SqlConnection
    $connection.ConnectionString = $mainDbConn
    $connection.Open()

    $command = $connection.CreateCommand()
    $command.CommandText = @"
WITH StockData AS (
    SELECT 
        r.Raw<PERSON>,
        r.<PERSON>,
        r<PERSON>,
        CASE 
            WHEN c.WarehouseType = 'Dept / WIP' AND c.Warehouse = 'Production - PRK2' THEN 'Production - PRK2'
            WHEN c.WarehouseType = 'Dept / WIP' AND c.Warehouse = 'Quality - PRK2' THEN 'Quality - PRK2'
            WHEN c.WarehouseType = 'Subcon' THEN c.Warehouse
            WHEN c.ProcessNo = 'FG' THEN c.Warehouse
        END AS LocationType,
        SUM(c.Stock) AS Stock,
        SUM(c.StkVal) AS StkVal
    FROM (
        SELECT WarehouseType, Warehouse, RawMatCode, RawMatName, ProcessNo, Stock, StkVal 
        FROM RealTimeWarehouse
        UNION ALL
        SELECT WarehouseType, Warehouse, RawMatCode, RawMatName, ProcessNo, Stock, StkVal 
        FROM RealTimeWIP
        UNION ALL
        SELECT WarehouseType, Warehouse, RawMatCode, RawMatName, ProcessNo, Stock, StkVal 
        FROM RealTimeSubcon
    ) c
    JOIN RawMaterial r ON c.RawMatCode = r.RawMatCode
    WHERE c.RawMatCode IN ('F019DB1302','F019DB1310','F019DB400A','F019DB4301')
      AND c.Warehouse NOT IN ('Scrap- PRK2')
    GROUP BY 
        r.RawMatCode,
        r.RawMatName,
        r.SellingRate,
        CASE 
            WHEN c.WarehouseType = 'Dept / WIP' AND c.Warehouse = 'Production - PRK2' THEN 'Production - PRK2'
            WHEN c.WarehouseType = 'Dept / WIP' AND c.Warehouse = 'Quality - PRK2' THEN 'Quality - PRK2'
            WHEN c.WarehouseType = 'Subcon' THEN c.Warehouse
            WHEN c.ProcessNo = 'FG' THEN c.Warehouse
        END
),
DistinctParts AS (
    SELECT DISTINCT
        RawMatCode,
        RawMatName,
        SellingRate,
        CASE 
            WHEN RawMatCode = 'F019DB1302' THEN 'F019DB1302'
            WHEN RawMatCode = 'F019DB1310' THEN 'F019DB1310'
            WHEN RawMatCode = 'F019DB400A' THEN 'F019DB400A'
            WHEN RawMatCode = 'F019DB4301' THEN 'F019DB4301'
        END AS FormattedPartNo,
        CASE 
            WHEN RawMatCode IN ('F019DB1302','F019DB1310') THEN 'F 019 DB1 301'
            WHEN RawMatCode IN ('F019DB400A','F019DB4301') THEN 'F 019 DB4 300'
        END AS ParentPartNo
    FROM StockData
),
MainData AS (
    SELECT 
        dp.FormattedPartNo,
        dp.ParentPartNo,
        dp.RawMatName,
        dp.SellingRate,
        -- PRODUCTION WIP
        MAX(CASE WHEN sd.LocationType = 'Production - PRK2' THEN sd.Stock END) AS [PRODUCTION_WIP_Qty],
        MAX(CASE WHEN sd.LocationType = 'Production - PRK2' THEN sd.StkVal END) AS [PRODUCTION_WIP_Val],
        -- QUALITY WIP
        MAX(CASE WHEN sd.LocationType = 'Quality - PRK2' THEN sd.Stock END) AS [QUALITY_WIP_Qty],
        MAX(CASE WHEN sd.LocationType = 'Quality - PRK2' THEN sd.StkVal END) AS [QUALITY_WIP_Val],
        -- EXTRUDE HONE
        MAX(CASE WHEN sd.LocationType = 'EXTRUDE HONE INDIA PVT LTD' THEN sd.Stock END) AS [EXTRUDE_HONE_Qty],
        MAX(CASE WHEN sd.LocationType = 'EXTRUDE HONE INDIA PVT LTD' THEN sd.StkVal END) AS [EXTRUDE_HONE_Val],
        -- M.L TECHNO
        MAX(CASE WHEN sd.LocationType = 'M.L Techno' THEN sd.Stock END) AS [ML_TECHNO_Qty],
        MAX(CASE WHEN sd.LocationType = 'M.L Techno' THEN sd.StkVal END) AS [ML_TECHNO_Val],
        -- NON MOVING STORE
        MAX(CASE WHEN sd.LocationType = 'Non Moving Stores' THEN sd.Stock END) AS [NON_MOVING_Qty],
        MAX(CASE WHEN sd.LocationType = 'Non Moving Stores' THEN sd.StkVal END) AS [NON_MOVING_Val],
        -- FINAL
        MAX(CASE WHEN sd.LocationType = 'Finish Goods Store - PRK2' THEN sd.Stock END) AS [FINAL_Qty],
        MAX(CASE WHEN sd.LocationType = 'Finish Goods Store - PRK2' THEN sd.StkVal END) AS [FINAL_Val]
    FROM DistinctParts dp
    LEFT JOIN StockData sd ON dp.RawMatCode = sd.RawMatCode
    GROUP BY 
        dp.FormattedPartNo,
        dp.ParentPartNo,
        dp.RawMatName,
        dp.SellingRate
),
FinalData AS (
    -- Main data with formatting
    SELECT 
        0 AS SortOrder,
        FormattedPartNo AS [Forgings Part Number],
        ParentPartNo AS [Parent Part Number],
        RawMatName AS [Part Name],
        ISNULL('Rs.' + dbo.FormatNumberIndian(SellingRate), 'NO VALUE') AS [Selling Rate],
        
        -- PRODUCTION WIP
        ISNULL(CAST([PRODUCTION_WIP_Qty] AS VARCHAR), 'NO STOCK') AS [PRODUCTION WIP (Qty)],
        ISNULL('Rs.' + dbo.FormatNumberIndian([PRODUCTION_WIP_Val]), 'NO VALUE') AS [PRODUCTION WIP (Value)],
        
        -- QUALITY WIP
        ISNULL(CAST([QUALITY_WIP_Qty] AS VARCHAR), 'NO STOCK') AS [QUALITY WIP (Qty)],
        ISNULL('Rs.' + dbo.FormatNumberIndian([QUALITY_WIP_Val]), 'NO VALUE') AS [QUALITY WIP (Value)],
        
        -- EXTRUDE HONE
        ISNULL(CAST([EXTRUDE_HONE_Qty] AS VARCHAR), 'NO STOCK') AS [EXTRUDE HONE (Qty)],
        ISNULL('Rs.' + dbo.FormatNumberIndian([EXTRUDE_HONE_Val]), 'NO VALUE') AS [EXTRUDE HONE (Value)],
        
        -- M.L TECHNO
        ISNULL(CAST([ML_TECHNO_Qty] AS VARCHAR), 'NO STOCK') AS [M.L TECHNO (Qty)],
        ISNULL('Rs.' + dbo.FormatNumberIndian([ML_TECHNO_Val]), 'NO VALUE') AS [M.L TECHNO (Value)],
        
        -- NON MOVING STORE
        ISNULL(CAST([NON_MOVING_Qty] AS VARCHAR), 'NO STOCK') AS [NON MOVING STORE (Qty)],
        ISNULL('Rs.' + dbo.FormatNumberIndian([NON_MOVING_Val]), 'NO VALUE') AS [NON MOVING STORE (Value)],
        
        -- FINAL
        ISNULL(CAST([FINAL_Qty] AS VARCHAR), 'NO STOCK') AS [FINAL (Qty)],
        ISNULL('Rs.' + dbo.FormatNumberIndian([FINAL_Val]), 'NO VALUE') AS [FINAL (Value)]
    FROM MainData
    
    UNION ALL
    
    -- Grand totals row
    SELECT 
        1 AS SortOrder,
        '' AS [Forgings Part Number],
        'GRAND TOTAL' AS [Parent Part Number],
        '' AS [Part Name],
        '' AS [Selling Rate],
        
        -- PRODUCTION WIP Qty
        CAST(SUM(ISNULL([PRODUCTION_WIP_Qty], 0)) AS VARCHAR) AS [PRODUCTION WIP (Qty)],
        -- PRODUCTION WIP Val
        'Rs.' + dbo.FormatNumberIndian(SUM(ISNULL([PRODUCTION_WIP_Val], 0))) AS [PRODUCTION WIP (Value)],
        
        -- QUALITY WIP Qty
        CAST(SUM(ISNULL([QUALITY_WIP_Qty], 0)) AS VARCHAR) AS [QUALITY WIP (Qty)],
        -- QUALITY WIP Val
        'Rs.' + dbo.FormatNumberIndian(SUM(ISNULL([QUALITY_WIP_Val], 0))) AS [QUALITY WIP (Value)],
        
        -- EXTRUDE HONE Qty
        CAST(SUM(ISNULL([EXTRUDE_HONE_Qty], 0)) AS VARCHAR) AS [EXTRUDE HONE (Qty)],
        -- EXTRUDE HONE Val
        'Rs.' + dbo.FormatNumberIndian(SUM(ISNULL([EXTRUDE_HONE_Val], 0))) AS [EXTRUDE HONE (Value)],
        
        -- M.L TECHNO Qty
        CAST(SUM(ISNULL([ML_TECHNO_Qty], 0)) AS VARCHAR) AS [M.L TECHNO (Qty)],
        -- M.L TECHNO Val
        'Rs.' + dbo.FormatNumberIndian(SUM(ISNULL([ML_TECHNO_Val], 0))) AS [M.L TECHNO (Value)],
        
        -- NON MOVING STORE Qty
        CAST(SUM(ISNULL([NON_MOVING_Qty], 0)) AS VARCHAR) AS [NON MOVING STORE (Qty)],
        -- NON MOVING STORE Val
        'Rs.' + dbo.FormatNumberIndian(SUM(ISNULL([NON_MOVING_Val], 0))) AS [NON MOVING STORE (Value)],
        
        -- FINAL Qty
        CAST(SUM(ISNULL([FINAL_Qty], 0)) AS VARCHAR) AS [FINAL (Qty)],
        -- FINAL Val
        'Rs.' + dbo.FormatNumberIndian(SUM(ISNULL([FINAL_Val], 0))) AS [FINAL (Value)]
    FROM MainData
)

SELECT 
    [Parent Part Number],
    [Forgings Part Number] + ' ' + [Part Name] AS [Forging Part],
    [Selling Rate],
    [PRODUCTION WIP (Qty)],
    [PRODUCTION WIP (Value)],
    [QUALITY WIP (Qty)],
    [QUALITY WIP (Value)],
    [EXTRUDE HONE (Qty)],
    [EXTRUDE HONE (Value)],
    [M.L TECHNO (Qty)],
    [M.L TECHNO (Value)],
    [NON MOVING STORE (Qty)],
    [NON MOVING STORE (Value)],
    [FINAL (Qty)],
    [FINAL (Value)],
    SortOrder
FROM FinalData
ORDER BY 
    SortOrder,
    [Parent Part Number],
    [Forgings Part Number];
"@

    $adapter = New-Object System.Data.SqlClient.SqlDataAdapter $command
    $dataset = New-Object System.Data.DataSet
    $adapter.Fill($dataset) | Out-Null
    $results = $dataset.Tables[0]
}
catch {
    $errorMessage = $_.Exception.Message
    Write-Error "Error executing SQL query: $errorMessage"
    exit
}
finally {
    if ($connection.State -eq 'Open') {
        $connection.Close()
    }
}

$htmlHeader = @"
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Barrel Stock Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            color: #333;
        }
        .report-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .report-date {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th {
            background-color: #f2f2f2;
            padding: 10px;
            text-align: left;
            border-bottom: 2px solid #ddd;
            font-weight: bold;
        }
        td {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }
        .parent-row {
            background-color: #e3f2fd;
            font-weight: bold;
        }
        .grand-total-row {
            background-color: #d4edda;
            font-weight: bold;
            color: #155724;
        }
        .grand-total-row td {
            border-top: 2px solid #155724;
            border-bottom: 2px solid #155724;
        }
        .part-number {
            font-weight: bold;
            color: #1a5276;
        }
        .no-stock {
            color: #e74c3c;
            font-style: italic;
        }
        .no-value {
            color: #f39c12;
            font-style: italic;
        }
        .currency {
            text-align: right;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="report-title">Barrel Stock Report</div>
    <div class="report-date">Generated on $(Get-Date -Format "dd MMM yyyy 'at' HH:mm")</div>
    
    <table>
        <thead>
            <tr>
                <th>Parent Part</th>
                <th>Forging Part</th>
                <th>Selling Rate</th>
                <th>PROD WIP (Qty)</th>
                <th>PROD WIP (Value)</th>
                <th>QUAL WIP (Qty)</th>
                <th>QUAL WIP (Value)</th>
                <th>EXTRUDE HONE(Qty)</th>
                <th>EXTRUDE HONE(Value)</th>
                <th>M.L TECHNO (Qty)</th>
                <th>M.L TECHNO (Value)</th>
                <th>NON MOVING (Qty)</th>
                <th>NON MOVING (Value)</th>
                <th>FINAL STORES (Qty)</th>
                <th>FINAL STORES(Value)</th>
            </tr>
        </thead>
        <tbody>
"@

# Process the data
$groupedData = @{}
$grandTotalRow = $null

foreach ($row in $results) {
    if ($row.'Parent Part Number' -eq 'GRAND TOTAL') {
        $grandTotalRow = $row
    } else {
        if (-not $groupedData.ContainsKey($row.'Parent Part Number')) {
            $groupedData[$row.'Parent Part Number'] = @()
        }
        $groupedData[$row.'Parent Part Number'] += $row
    }
}

# Generate HTML rows
$htmlBody = ""
foreach ($parentPart in $groupedData.Keys) {
    # Add parent row
    $htmlBody += @"
            <tr class="parent-row">
                <td colspan="15">$parentPart</td>
            </tr>
"@

    # Add child rows
    foreach ($childRow in $groupedData[$parentPart]) {
        # Split forging part into number and name
        $forgingParts = $childRow.'Forging Part' -split '(?=\s[A-Za-z])', 2
        $partNumber = $forgingParts[0]
        $partName = if ($forgingParts.Count -gt 1) { $forgingParts[1] } else { "" }
        
        $htmlBody += @"
            <tr>
                <td></td>
                <td>
                    <span class="part-number">$partNumber</span>
                    <span>$partName</span>
                </td>
                <td>$($childRow.'Selling Rate')</td>
                <td>$($childRow.'PRODUCTION WIP (Qty)')</td>
                <td class="currency">$($childRow.'PRODUCTION WIP (Value)')</td>
                <td>$($childRow.'QUALITY WIP (Qty)')</td>
                <td class="currency">$($childRow.'QUALITY WIP (Value)')</td>
                <td>$($childRow.'EXTRUDE HONE (Qty)')</td>
                <td class="currency">$($childRow.'EXTRUDE HONE (Value)')</td>
                <td>$($childRow.'M.L TECHNO (Qty)')</td>
                <td class="currency">$($childRow.'M.L TECHNO (Value)')</td>
                <td>$($childRow.'NON MOVING STORE (Qty)')</td>
                <td class="currency">$($childRow.'NON MOVING STORE (Value)')</td>
                <td>$($childRow.'FINAL (Qty)')</td>
                <td class="currency">$($childRow.'FINAL (Value)')</td>
            </tr>
"@
    }
}

# Add Grand Total row to the table
if ($grandTotalRow) {
    $htmlBody += @"
            <tr class="grand-total-row">
                <td><strong>$($grandTotalRow.'Parent Part Number')</strong></td>
                <td></td>
                <td></td>
                <td><strong>$($grandTotalRow.'PRODUCTION WIP (Qty)')</strong></td>
                <td class="currency"><strong>$($grandTotalRow.'PRODUCTION WIP (Value)')</strong></td>
                <td><strong>$($grandTotalRow.'QUALITY WIP (Qty)')</strong></td>
                <td class="currency"><strong>$($grandTotalRow.'QUALITY WIP (Value)')</strong></td>
                <td><strong>$($grandTotalRow.'EXTRUDE HONE (Qty)')</strong></td>
                <td class="currency"><strong>$($grandTotalRow.'EXTRUDE HONE (Value)')</strong></td>
                <td><strong>$($grandTotalRow.'M.L TECHNO (Qty)')</strong></td>
                <td class="currency"><strong>$($grandTotalRow.'M.L TECHNO (Value)')</strong></td>
                <td><strong>$($grandTotalRow.'NON MOVING STORE (Qty)')</strong></td>
                <td class="currency"><strong>$($grandTotalRow.'NON MOVING STORE (Value)')</strong></td>
                <td><strong>$($grandTotalRow.'FINAL (Qty)')</strong></td>
                <td class="currency"><strong>$($grandTotalRow.'FINAL (Value)')</strong></td>
            </tr>
"@
}

$htmlFooter = @"
        </tbody>
    </table>
</body>
</html>
"@

$finalHtml = $htmlHeader + $htmlBody + $htmlFooter

# Send Email
try {
    Send-MailMessage -From $from -To $to -Subject $subject -Body $finalHtml -BodyAsHtml `
        -SmtpServer $smtpServer -Port $smtpPort -UseSsl -Credential $creds
    
    Write-Host "Email sent successfully!"
}
catch {
    Write-Error "Failed to send email: $($_.Exception.Message)"
}