-- ULTRA SIMPLE Material PO Query - NO APPROVAL LOGIC
-- Just get all pending Material POs
USE Icsoft;

SELECT
    COM.LOCATION,
    d.<PERSON>d,
    d<PERSON><PERSON>,
    d<PERSON>,
    d.<PERSON>,
    d<PERSON>,
    SupName + ' | ' + SupCode AS gStrSupplierDisp,
    RawMatCode + ' | ' + RawMatName AS gStrMatDisp,
    GCode + ' | ' + Gradename AS gStrGradeDisp,
    d.<PERSON>_<PERSON>,
    d.<PERSON>,
    ROUND(d.Rate, 4) AS Rate,
    d.CurrCode AS Currency,
    d.Rate AS price,
    ROUND((d.Ord_Qty * d.Rate), 2) AS povalue,
    d.RawMatId,
    d.<PERSON>Po<PERSON>alue,
    d.<PERSON>NAME AS POCreatedBY,
    d.HSNID,
    GH.HSNCode,
    Terms,
    QuotTerms
FROM
    Invent_PoDetails_Query d
    LEFT OUTER JOIN GST_HSN_SAC_NO GH ON GH.Hsn_Sac_ID = d.HSNID
    INNER JOIN COMPANY COM ON COM.CompanyID = D.LocationID
WHERE
    d.PoStatus = 'Approval Pending'
ORDER BY
    d.PoDate DESC;
