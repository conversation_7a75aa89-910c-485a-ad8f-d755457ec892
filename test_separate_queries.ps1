# Test separate Material and Service PO queries
. "C:\Users\<USER>\Documents\augment-projects\PO APPROVAL\database_service.ps1"

Write-Host "=== TESTING SEPARATE QUERIES ===" -ForegroundColor Yellow

# Test 1: Simple Material PO query (without complex approval logic)
Write-Host "Testing simplified Material PO query..." -ForegroundColor Cyan
try {
    $connection = Get-DatabaseConnection
    $materialQuery = @"
    SELECT TOP 10
        COM.LOCATION,
        d.Po<PERSON>d,
        d.<PERSON>,
        d.<PERSON>,
        d.<PERSON>,
        SupName + ' | ' + SupCode AS gStrSupplierDisp,
        RawMatCode + ' | ' + RawMatName AS gStrMatDisp,
        GCode + ' | ' + Gradename AS gStrGradeDisp,
        d.<PERSON>_<PERSON>,
        d.<PERSON>,
        ROUND(d.Rate, 4) AS Rate,
        d.CurrCode AS Currency,
        ROUND((d.Ord_Qty * d.Rate), 2) AS povalue,
        d.<PERSON>,
        d.<PERSON>o<PERSON>,
        d.<PERSON>MP<PERSON>ME AS POCreatedBY,
        d.H<PERSON>,
        GH.HSNCode,
        Terms,
        QuotTerms
    FROM
        Invent_PoDetails_Query d
        LEFT OUTER JOIN GST_HSN_SAC_NO GH ON GH.Hsn_Sac_ID = d.HSNID
        INNER JOIN COMPANY COM ON COM.CompanyID = D.LocationID
    WHERE
        d.PoStatus = 'Approval Pending'
    ORDER BY d.PoDate DESC
"@
    
    $command = New-Object System.Data.SqlClient.SqlCommand($materialQuery, $connection)
    $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
    $dataTable = New-Object System.Data.DataTable
    $adapter.Fill($dataTable)
    $connection.Close()
    
    Write-Host "Material POs found: $($dataTable.Rows.Count)" -ForegroundColor Green
    
    if ($dataTable.Rows.Count -gt 0) {
        $row = $dataTable.Rows[0]
        Write-Host "Sample Material PO:" -ForegroundColor Yellow
        Write-Host "  PoId: $($row['PoId'])" -ForegroundColor White
        Write-Host "  PoNo: $($row['PoNo'])" -ForegroundColor White
        Write-Host "  Supplier: $($row['gStrSupplierDisp'])" -ForegroundColor White
        Write-Host "  Material: $($row['gStrMatDisp'])" -ForegroundColor White
    }
} catch {
    Write-Host "Material PO Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Simple Service PO query (without complex approval logic)
Write-Host "Testing simplified Service PO query..." -ForegroundColor Cyan
try {
    $connection = Get-DatabaseConnection
    $serviceQuery = @"
    SELECT TOP 10
        com.Location,
        iscp.poid,
        iscp.Pono,
        iscp.Podate,
        iscp.addnlparameter,
        sc.supname + ' | ' + sc.supcode AS gStrSupplierDisp,
        ISNULL(r.rawmatcode + ' | ' + r.rawmatname, 'Service Item') AS gStrMatDisp,
        '-' AS grade,
        ISNULL(ispp.Ord_Qty, 1) AS Ord_Qty,
        ISNULL(ispp.UOM, 'Service') AS Uom,
        ISNULL(ispp.rate, 0) AS Rate,
        'INR' AS Currency,
        ISNULL(ispp.rate * ispp.Ord_Qty, 0) AS povalue,
        ISNULL(ispp.RawmatId, 0) AS RawmatId,
        ISNULL(iscp.TotalSubconPurchaseValue, 0) AS TotalSubconPurchaseValue,
        e.empname AS pocreatedby,
        NULL AS ServiceHSNID,
        NULL AS HSNCode,
        NULL AS Terms
    FROM 
        invent_subconpurchase iscp
        INNER JOIN invent_supplier sc ON sc.supid = iscp.subconid
        INNER JOIN company com ON com.CompanyID = iscp.locationid
        INNER JOIN Employee e ON e.EmpId = iscp.entryempid 
        LEFT OUTER JOIN invent_subconpurchaseproduct ispp ON iscp.poid = ispp.poid 
        LEFT OUTER JOIN rawmaterial r ON ispp.rawmatId = r.rawmatId 
    WHERE 
        iscp.PoStatus = 'Approval Pending'
        AND iscp.addnlparameter = 'General PO Service'
    ORDER BY iscp.PoDate DESC
"@
    
    $command = New-Object System.Data.SqlClient.SqlCommand($serviceQuery, $connection)
    $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
    $dataTable = New-Object System.Data.DataTable
    $adapter.Fill($dataTable)
    $connection.Close()
    
    Write-Host "Service POs found: $($dataTable.Rows.Count)" -ForegroundColor Green
    
    if ($dataTable.Rows.Count -gt 0) {
        $row = $dataTable.Rows[0]
        Write-Host "Sample Service PO:" -ForegroundColor Yellow
        Write-Host "  PoId: $($row['poid'])" -ForegroundColor White
        Write-Host "  PoNo: $($row['Pono'])" -ForegroundColor White
        Write-Host "  Supplier: $($row['gStrSupplierDisp'])" -ForegroundColor White
        Write-Host "  Material: $($row['gStrMatDisp'])" -ForegroundColor White
    }
} catch {
    Write-Host "Service PO Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Test completed!" -ForegroundColor Green
