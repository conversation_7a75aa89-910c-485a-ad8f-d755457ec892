# Debug script to test queries step by step
$ErrorActionPreference = "Continue"

Write-Host "=== PO APPROVAL QUERY DEBUGGING ===" -ForegroundColor Yellow

# Load database service
try {
    . "C:\Users\<USER>\Documents\augment-projects\PO APPROVAL\database_service.ps1"
    Write-Host "✅ Database service loaded" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to load database service: $($_.Exception.Message)" -ForegroundColor Red
    exit
}

# Test database connection
try {
    $connection = Get-DatabaseConnection
    Write-Host "✅ Database connection successful" -ForegroundColor Green
    $connection.Close()
} catch {
    Write-Host "❌ Database connection failed: $($_.Exception.Message)" -ForegroundColor Red
    exit
}

# Test 1: Simple count of pending Material POs
Write-Host "`n--- Test 1: Simple Material PO Count ---" -ForegroundColor Cyan
try {
    $connection = Get-DatabaseConnection
    $query1 = "SELECT COUNT(*) as Count FROM Invent_PoDetails_Query WHERE PoStatus = 'Approval Pending'"
    $command1 = New-Object System.Data.SqlClient.SqlCommand($query1, $connection)
    $count1 = $command1.ExecuteScalar()
    $connection.Close()
    Write-Host "Material POs with 'Approval Pending': $count1" -ForegroundColor White
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Simple count of pending Service POs
Write-Host "`n--- Test 2: Simple Service PO Count ---" -ForegroundColor Cyan
try {
    $connection = Get-DatabaseConnection
    $query2 = "SELECT COUNT(*) as Count FROM invent_subconpurchase WHERE PoStatus = 'Approval Pending'"
    $command2 = New-Object System.Data.SqlClient.SqlCommand($query2, $connection)
    $count2 = $command2.ExecuteScalar()
    $connection.Close()
    Write-Host "Service POs with 'Approval Pending': $count2" -ForegroundColor White
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Test Material PO query without approval logic
Write-Host "`n--- Test 3: Simplified Material PO Query ---" -ForegroundColor Cyan
try {
    $connection = Get-DatabaseConnection
    $query3 = @"
    SELECT TOP 5
        d.PoId,
        d.PoNo,
        d.PoStatus,
        d.Addnlparameter,
        d.EMPNAME
    FROM Invent_PoDetails_Query d
    WHERE d.PoStatus = 'Approval Pending'
    ORDER BY d.PoDate DESC
"@
    $command3 = New-Object System.Data.SqlClient.SqlCommand($query3, $connection)
    $adapter3 = New-Object System.Data.SqlClient.SqlDataAdapter($command3)
    $dataTable3 = New-Object System.Data.DataTable
    $adapter3.Fill($dataTable3)
    $connection.Close()
    
    Write-Host "Simplified Material POs found: $($dataTable3.Rows.Count)" -ForegroundColor White
    if ($dataTable3.Rows.Count -gt 0) {
        foreach ($row in $dataTable3.Rows) {
            Write-Host "  PoId: $($row['PoId']), PoNo: $($row['PoNo']), Status: $($row['PoStatus'])" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Test Service PO query without approval logic
Write-Host "`n--- Test 4: Simplified Service PO Query ---" -ForegroundColor Cyan
try {
    $connection = Get-DatabaseConnection
    $query4 = @"
    SELECT TOP 5
        iscp.poid,
        iscp.Pono,
        iscp.PoStatus,
        iscp.addnlparameter,
        e.empname
    FROM invent_subconpurchase iscp
    INNER JOIN Employee e ON e.EmpId = iscp.entryempid
    WHERE iscp.PoStatus = 'Approval Pending'
    ORDER BY iscp.PoDate DESC
"@
    $command4 = New-Object System.Data.SqlClient.SqlCommand($query4, $connection)
    $adapter4 = New-Object System.Data.SqlClient.SqlDataAdapter($command4)
    $dataTable4 = New-Object System.Data.DataTable
    $adapter4.Fill($dataTable4)
    $connection.Close()
    
    Write-Host "Simplified Service POs found: $($dataTable4.Rows.Count)" -ForegroundColor White
    if ($dataTable4.Rows.Count -gt 0) {
        foreach ($row in $dataTable4.Rows) {
            Write-Host "  PoId: $($row['poid']), PoNo: $($row['Pono']), Status: $($row['PoStatus'])" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Test complex Material PO query
Write-Host "`n--- Test 5: Complex Material PO Query ---" -ForegroundColor Cyan
try {
    $connection = Get-DatabaseConnection
    $materialQuery = Get-Content "material_po_query.sql" -Raw
    $command5 = New-Object System.Data.SqlClient.SqlCommand($materialQuery, $connection)
    $adapter5 = New-Object System.Data.SqlClient.SqlDataAdapter($command5)
    $dataTable5 = New-Object System.Data.DataTable
    $adapter5.Fill($dataTable5)
    $connection.Close()
    
    Write-Host "Complex Material POs found: $($dataTable5.Rows.Count)" -ForegroundColor White
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Test complex Service PO query
Write-Host "`n--- Test 6: Complex Service PO Query ---" -ForegroundColor Cyan
try {
    $connection = Get-DatabaseConnection
    $serviceQuery = Get-Content "service_po_query.sql" -Raw
    $command6 = New-Object System.Data.SqlClient.SqlCommand($serviceQuery, $connection)
    $adapter6 = New-Object System.Data.SqlClient.SqlDataAdapter($command6)
    $dataTable6 = New-Object System.Data.DataTable
    $adapter6.Fill($dataTable6)
    $connection.Close()
    
    Write-Host "Complex Service POs found: $($dataTable6.Rows.Count)" -ForegroundColor White
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== DEBUGGING COMPLETE ===" -ForegroundColor Yellow
