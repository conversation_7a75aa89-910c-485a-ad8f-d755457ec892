# Test direct database query to find pending POs
. "C:\Users\<USER>\Documents\augment-projects\PO APPROVAL\database_service.ps1"

Write-Host "=== DIRECT DATABASE QUERY TEST ===" -ForegroundColor Yellow

try {
    $connection = Get-DatabaseConnection

    Write-Host "Testing simplified combined query..." -ForegroundColor Cyan

    $query = @"
    SELECT TOP 10
        d.<PERSON>,
        d.<PERSON>,
        d.<PERSON>,
        d.Addnlparameter AS POType,
        d.SupName AS Supplier,
        d.TotalPoValue AS TotalValue,
        d.EMPNAME AS CreatedBy,
        'Unknown' AS Location,
        'Material' AS POCategory
    FROM Invent_PoDetails_Query d
    WHERE d.PoStatus = 'Approval Pending'

    UNION ALL

    SELECT TOP 10
        iscp.poid AS PoId,
        iscp.Pono AS PoNo,
        iscp.Podate AS PoDate,
        iscp.addnlparameter AS POType,
        sc.supname AS Supplier,
        iscp.TotalSubconPurchaseValue AS TotalValue,
        e.empname AS CreatedBy,
        com.Location AS Location,
        'Service' AS POCategory
    FROM invent_subconpurchase iscp
    INNER JOIN invent_supplier sc ON sc.supid = iscp.subconid
    INNER JOIN Employee e ON e.EmpId = iscp.entryempid
    INNER JOIN company com ON com.CompanyID = iscp.locationid
    WHERE iscp.PoStatus = 'Approval Pending'

    ORDER BY PoDate DESC
"@

    $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
    $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
    $dataTable = New-Object System.Data.DataTable
    $adapter.Fill($dataTable)
    $connection.Close()

    Write-Host "Query returned $($dataTable.Rows.Count) rows" -ForegroundColor Green

    if ($dataTable.Rows.Count -gt 0) {
        Write-Host "Sample results:" -ForegroundColor Cyan
        for ($i = 0; $i -lt [Math]::Min(5, $dataTable.Rows.Count); $i++) {
            $row = $dataTable.Rows[$i]
            Write-Host "  Row $($i+1):" -ForegroundColor Yellow
            Write-Host "    PoId: $($row['PoId'])" -ForegroundColor White
            Write-Host "    PoNo: $($row['PoNo'])" -ForegroundColor White
            Write-Host "    Supplier: $($row['Supplier'])" -ForegroundColor White
            Write-Host "    POType: $($row['POType'])" -ForegroundColor White
            Write-Host "    Category: $($row['POCategory'])" -ForegroundColor White
            Write-Host "    TotalValue: $($row['TotalValue'])" -ForegroundColor White
            Write-Host ""
        }
    } else {
        Write-Host "No results found!" -ForegroundColor Red

        # Test individual queries
        Write-Host "Testing Material POs only..." -ForegroundColor Yellow
        $materialQuery = "SELECT TOP 5 PoId, PoNo, SupName, Addnlparameter FROM Invent_PoDetails_Query WHERE PoStatus = 'Approval Pending'"
        $materialCommand = New-Object System.Data.SqlClient.SqlCommand($materialQuery, $connection)
        $connection.Open()
        $materialAdapter = New-Object System.Data.SqlClient.SqlDataAdapter($materialCommand)
        $materialTable = New-Object System.Data.DataTable
        $materialAdapter.Fill($materialTable)
        $connection.Close()
        Write-Host "Material POs found: $($materialTable.Rows.Count)" -ForegroundColor Cyan

        Write-Host "Testing Service POs only..." -ForegroundColor Yellow
        $connection.Open()
        $serviceQuery = "SELECT TOP 5 poid, Pono, addnlparameter FROM invent_subconpurchase WHERE PoStatus = 'Approval Pending'"
        $serviceCommand = New-Object System.Data.SqlClient.SqlCommand($serviceQuery, $connection)
        $serviceAdapter = New-Object System.Data.SqlClient.SqlDataAdapter($serviceCommand)
        $serviceTable = New-Object System.Data.DataTable
        $serviceAdapter.Fill($serviceTable)
        $connection.Close()
        Write-Host "Service POs found: $($serviceTable.Rows.Count)" -ForegroundColor Cyan
    }

} catch {
    Write-Host "Error in direct query test:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    if ($connection.State -eq 'Open') { $connection.Close() }
}
