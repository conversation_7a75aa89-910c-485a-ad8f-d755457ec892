-- Service PO Approval Query
-- Server: WIN-PRK-SRV-01
-- Database: Icsoft
-- Description: Query to fetch Service PO approval details

USE Icsoft;

SELECT
    location,
    t1.poid,
    t1.pono,
    t1.podate,
    t1.postatus,  -- Added PO Status column
    t1.addnlparameter,
    SupName + ' | ' + SupCode AS gStrSupplierDisp,
    RawMatCode + ' | ' + RawMatName AS gStrMatDisp,
    '-' AS grade,
    t1.Ord_Qty,
    t1.Uom,
    t1.Rate,
    'INR' AS Currency,
    t1.Rate AS Price,
    t1.povalue,
    RawmatId,
    TotalSubconPurchaseValue,
    empname AS pocreatedby,
    ServiceHSNID,
    HSNCode,
    Terms
FROM
    (SELECT
        com.Location,
        sc.supcode,
        sc.supid,
        sc.supname,
        iscp.addnlparameter,
        iscp.postatus,
        iscp.LocationId,
        iscp.poid,
        iscp.Pono,
        iscp.Podate,
        ispp.Ord_Qty,
        ispp.UOM,
        ispp.rate,
        ROUND((ispp.Ord_Qty * ((ispp.Rate + ispp.pack) - Discount)), 2) AS povalue,
        ispp.Poproductid,
        r.rawmatcode,
        r.rawmatname,
        ispp.subcondesc,
        R.RawmatId,
        empname,
        ispp.ServiceHSNID,
        iscp.TotalSubconPurchaseValue,
        GH.HSNCode,
        Act.CostCentre + ' | ' + Act.Description AS ActCostCentreDisplay,
        Terms,
        sc.supname AS SupName,
        sc.supcode AS SupCode
    FROM
        invent_supplier sc
        INNER JOIN invent_subconpurchase iscp ON sc.supid = iscp.subconid
        INNER JOIN company com ON com.CompanyID = iscp.locationid
        INNER JOIN Employee e ON e.EmpId = iscp.entryempid
        INNER JOIN invent_subconpurchaseproduct ispp ON iscp.poid = ispp.poid
        LEFT OUTER JOIN rawmaterial r ON ispp.rawmatId = r.rawmatId
        LEFT OUTER JOIN GST_HSN_SAC_NO GH ON GH.Hsn_Sac_ID = ispp.ServiceHSNID
        LEFT OUTER JOIN IcSoftLedger.dbo.Acc_CostCentre Act ON iscp.ProfitCentreId = Act.CostCentreId
    ) t1
    INNER JOIN (
        SELECT DISTINCT
            PS.EMpid,
            poid,
            MaxLimit,
            MinLimit
        FROM
            Invent_POApprovalLevels PE (NOLOCK)
            INNER JOIN Invent_POApprovalEmpLevel PS (NOLOCK) ON PE.levelid = PS.Levelid
            INNER JOIN invent_subconpurchase P ON P.GTotal = 0
                OR (
                    CASE
                        WHEN PE.IsBasic = 'N'
                            THEN (P.GTotal + (SELECT ISNULL(SUM(Amount), 0) FROM Invent_ServiceProdWiseTax T WHERE T.poid = P.Poid))
                            ELSE P.GTotal
                    END * CASE WHEN P.Exrate = 0 THEN 1 ELSE P.Exrate END <= (
                        SELECT MAX(Maxlimit)
                        FROM Invent_POApprovalLevels
                        WHERE Potype = P.AddnlParameter
                            AND Locationid LIKE '%'
                    )
                )
                AND (
                    CASE
                        WHEN PE.IsBasic = 'N'
                            THEN (P.GTotal + (SELECT ISNULL(SUM(Amount), 0) FROM Invent_ServiceProdWiseTax T WHERE T.poid = P.Poid))
                            ELSE P.GTotal
                    END * CASE WHEN P.Exrate = 0 THEN 1 ELSE P.Exrate END >= PE.Minlimit
                )
        WHERE
            postatus IN ('Approval Pending')
            AND PE.POType = P.AddnlParameter
            AND PE.LocationId LIKE '%'
            AND PE.Levelid NOT IN (
                SELECT DISTINCT Levelid
                FROM Invent_POApprovedEmployee A
                WHERE A.Poid = P.POiD
                    AND A.levelid = PE.Levelid
                    AND PE.LocationId = 3
            )
            AND PS.Levelid IN (
                SELECT TOP 1 PS.Levelid
                FROM Invent_POApprovalLevels PE (NOLOCK)
                INNER JOIN Invent_POApprovalEmpLevel PS (NOLOCK) ON PE.levelid = PS.Levelid
                WHERE
                    PS.Levelid = PE.levelid
                    AND PE.Locationid LIKE '%'
                    AND EMpid NOT IN (
                        SELECT DISTINCT Empid
                        FROM Invent_POApprovedEmployee A
                        WHERE A.Poid = P.POiD
                            AND A.levelid = PE.Levelid
                            AND PE.Locationid LIKE '%'
                    )
                    AND PoType = P.Addnlparameter
                    AND PS.Levelid NOT IN (
                        SELECT DISTINCT Levelid
                        FROM Invent_POApprovedEmployee A
                        WHERE A.Poid = P.POiD
                            AND A.levelid = PE.Levelid
                            AND PE.Locationid LIKE '%'
                    )
                ORDER BY PE.MinLimit, PE.MaxLimit
            )
    ) t2 ON t1.Poid = T2.poid
WHERE
    t1.Addnlparameter = 'General PO Service'
    AND t1.locationid LIKE '%'
    AND t1.PoStatus = 'Approval Pending'
ORDER BY
    t1.PoDate,
    t1.PoNo,
    t1.PoProductId;
